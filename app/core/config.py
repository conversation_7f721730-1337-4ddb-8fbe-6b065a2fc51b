from typing import Any, Dict, Optional
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, validator
from pydantic_neo4j import PydanticNeo4j


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "organisation-service"
    DEBUG: bool = False
    PORT: int = 50070

    # Database settings
    DB_HOST: str
    DB_PORT: str
    DB_USER: str
    DB_PASSWORD: str
    DB_NAME: str
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"DB_USER", "DB_PASSWORD", "DB_HOST", "DB_PORT", "DB_NAME"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required database configuration: {missing}")

        return PostgresDsn.build(
            scheme="postgresql",
            username=values.get("DB_USER"),
            password=values.get("DB_PASSWORD"),
            host=values.get("DB_HOST"),
            port=int(values.get("DB_PORT", 5432)),
            path=f"{values.get('DB_NAME')}",
        )

    # JWT settings
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Redis settings
    REDIS_HOST: str
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_JWT_ACCESS_EXPIRE_SEC: int = 3600
    REDIS_URI: Optional[str] = None  # Add this line

    @validator("REDIS_URI", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"REDIS_HOST", "REDIS_PORT", "REDIS_DB"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required Redis configuration: {missing}")

        auth_part = f":{values.get('REDIS_PASSWORD')}@" if values.get("REDIS_PASSWORD") else ""
        return f"redis://{auth_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT', 6379)}/{values.get('REDIS_DB', 0)}"
    
    # Neo4j settings
    NEO4J_URI: str
    NEO4J_USER: str
    NEO4J_PASSWORD: str
    NEO4j_DATABASE: str

    BOOTSTRAP_SERVERS: str = ""

    REPO_URL: str = ""
    GIT_TOKEN: str = ""

    API_GATEWAY_URL: str = ""

    FRONTEND_URL: str = ""

    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_REDIRECT_URI:str = ""
    
    ENCRYPTED_OTP_VALIDITY:int=60

    GOOGLE_APPLICATION_CREDENTIALS: str = "credentials.json"
    GOOGLE_PROJECT_ID: str = ""
    
    # Pinecone settings
    PINECONE_API_KEY: str = "pcsk_WYuwj_2wNfJcxcqYMtYjsWzGMxmzJ7tQHBFz2Hgjb6q3XaYQ78Z7NT6SbvSotquxbjMjq"
    PINECONE_ENVIRONMENT: str = "gcp-starter"
    PINECONE_INDEX_NAME: str = "document-embeddings"

    OPENAI_API_KEY: str = ""

    REQUESTY_API_KEY:str = "6ZvcYsx3QpOd5ACuCj4BgGUrqOUquHI9zJc4zM17IbcHGro78lhghNFgyI+yrzMoo8uoY4X88gQGZggLcjss07GbFtZ9mRDXpZxBvfiKEAI="

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"  # Allow extra fields in the environment



settings = Settings()
