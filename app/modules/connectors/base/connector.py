"""
Base connector framework for standardizing connector implementations.

This module provides abstract base classes and interfaces that all connectors
must implement to ensure consistent behavior and easy integration.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
import structlog

logger = structlog.get_logger()


class ConnectorType(Enum):
    """Enumeration of supported connector types."""
    GOOGLE_DRIVE = "google_drive"
    SLACK = "slack"
    GMAIL = "gmail"
    NOTION = "notion"
    CONFLUENCE = "confluence"
    SHAREPOINT = "sharepoint"


class ConnectorStatus(Enum):
    """Enumeration of connector status states."""
    INACTIVE = "inactive"
    ACTIVE = "active"
    ERROR = "error"
    SYNCING = "syncing"
    DISCONNECTED = "disconnected"


class BaseConnector(ABC):
    """
    Abstract base class for all connectors.
    
    This class defines the core interface that all connectors must implement
    to ensure consistent behavior across different data sources.
    """
    
    def __init__(self, connector_type: ConnectorType, name: str):
        """
        Initialize the base connector.
        
        Args:
            connector_type: The type of connector
            name: Human-readable name of the connector
        """
        self.connector_type = connector_type
        self.name = name
        self.status = ConnectorStatus.INACTIVE
        self._initialized = False
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """
        Initialize the connector with configuration.
        
        Args:
            config: Configuration dictionary containing connector-specific settings
            
        Returns:
            Boolean indicating if initialization was successful
        """
        pass
    
    @abstractmethod
    def test_connection(self) -> Tuple[bool, str]:
        """
        Test the connection to the data source.
        
        Returns:
            Tuple containing success status and message
        """
        pass
    
    @abstractmethod
    def sync_data(self, user_id: str, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str]:
        """
        Sync data from the connector source.
        
        Args:
            user_id: ID of the user initiating the sync
            organisation_id: ID of the organization
            full_sync: Whether to perform a full sync or incremental
            
        Returns:
            Tuple containing success status and message
        """
        pass
    
    @abstractmethod
    def disconnect(self, organisation_id: str) -> Tuple[bool, str]:
        """
        Disconnect and clean up connector data.
        
        Args:
            organisation_id: ID of the organization
            
        Returns:
            Tuple containing success status and message
        """
        pass
    
    @abstractmethod
    def get_sync_status(self, organisation_id: str) -> Dict[str, Any]:
        """
        Get the current sync status for an organization.
        
        Args:
            organisation_id: ID of the organization
            
        Returns:
            Dictionary containing sync status information
        """
        pass
    
    @abstractmethod
    def get_supported_file_types(self) -> List[str]:
        """
        Get list of file types supported by this connector.
        
        Returns:
            List of supported MIME types or file extensions
        """
        pass
    
    def is_initialized(self) -> bool:
        """Check if the connector is initialized."""
        return self._initialized
    
    def get_status(self) -> ConnectorStatus:
        """Get the current status of the connector."""
        return self.status
    
    def set_status(self, status: ConnectorStatus):
        """Set the connector status."""
        self.status = status
        logger.info(f"Connector {self.name} status changed to {status.value}")


class BaseConnectorService(ABC):
    """
    Abstract base class for connector services.
    
    This class provides the service layer interface for connectors,
    handling business logic and data processing.
    """
    
    def __init__(self, connector: BaseConnector):
        """
        Initialize the connector service.
        
        Args:
            connector: The base connector instance
        """
        self.connector = connector
    
    @abstractmethod
    def sync_file_by_id(self, file_id: str, agent_id: str, user_id: str, 
                       organisation_id: str, **kwargs) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Sync a specific file by its ID.
        
        Args:
            file_id: ID of the file to sync
            agent_id: ID of the agent
            user_id: ID of the user
            organisation_id: ID of the organization
            **kwargs: Additional connector-specific parameters
            
        Returns:
            Tuple containing success status, message, and file data
        """
        pass
    
    @abstractmethod
    def get_file_metadata(self, file_id: str, organisation_id: str) -> Dict[str, Any]:
        """
        Get metadata for a specific file.
        
        Args:
            file_id: ID of the file
            organisation_id: ID of the organization
            
        Returns:
            Dictionary containing file metadata
        """
        pass
    
    @abstractmethod
    def list_files(self, organisation_id: str, limit: int = 100, 
                  offset: int = 0) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        List files from the connector source.
        
        Args:
            organisation_id: ID of the organization
            limit: Maximum number of files to return
            offset: Number of files to skip
            
        Returns:
            Tuple containing success status, message, and list of files
        """
        pass


class BaseConnectorGrpcService(ABC):
    """
    Abstract base class for connector gRPC services.
    
    This class provides the gRPC interface for connectors,
    handling external API requests.
    """
    
    def __init__(self, connector_service: BaseConnectorService):
        """
        Initialize the gRPC service.
        
        Args:
            connector_service: The connector service instance
        """
        self.connector_service = connector_service
    
    @abstractmethod
    def sync_data(self, request, context):
        """Handle sync data gRPC request."""
        pass
    
    @abstractmethod
    def get_sync_status(self, request, context):
        """Handle get sync status gRPC request."""
        pass
    
    @abstractmethod
    def disconnect(self, request, context):
        """Handle disconnect gRPC request."""
        pass
