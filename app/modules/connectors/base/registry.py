"""
Connector Registry System

This module provides a centralized registry for managing all available connectors,
their initialization, and provides a unified interface for connector operations.
"""

from typing import Dict, List, Optional, Type, Any
import structlog
from .connector import BaseConnector, BaseConnectorService, BaseConnectorGrpcService, ConnectorType

logger = structlog.get_logger()


class ConnectorRegistry:
    """
    Centralized registry for managing all connectors in the system.
    
    This class provides a singleton pattern to ensure there's only one
    registry instance managing all connectors.
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConnectorRegistry, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._connectors: Dict[ConnectorType, BaseConnector] = {}
            self._connector_services: Dict[ConnectorType, BaseConnectorService] = {}
            self._grpc_services: Dict[ConnectorType, BaseConnectorGrpcService] = {}
            self._connector_classes: Dict[ConnectorType, Type[BaseConnector]] = {}
            self._service_classes: Dict[ConnectorType, Type[BaseConnectorService]] = {}
            self._grpc_classes: Dict[ConnectorType, Type[BaseConnectorGrpcService]] = {}
            ConnectorRegistry._initialized = True
    
    def register_connector(self, 
                          connector_type: ConnectorType,
                          connector_class: Type[BaseConnector],
                          service_class: Type[BaseConnectorService],
                          grpc_class: Type[BaseConnectorGrpcService]) -> bool:
        """
        Register a new connector type with the registry.
        
        Args:
            connector_type: The type of connector to register
            connector_class: The connector class
            service_class: The service class
            grpc_class: The gRPC service class
            
        Returns:
            Boolean indicating if registration was successful
        """
        try:
            if connector_type in self._connector_classes:
                logger.warning(f"Connector type {connector_type.value} already registered, overwriting")
            
            self._connector_classes[connector_type] = connector_class
            self._service_classes[connector_type] = service_class
            self._grpc_classes[connector_type] = grpc_class
            
            logger.info(f"Successfully registered connector: {connector_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register connector {connector_type.value}: {str(e)}")
            return False
    
    def initialize_connector(self, connector_type: ConnectorType, config: Dict[str, Any]) -> bool:
        """
        Initialize a specific connector with configuration.
        
        Args:
            connector_type: The type of connector to initialize
            config: Configuration dictionary
            
        Returns:
            Boolean indicating if initialization was successful
        """
        try:
            if connector_type not in self._connector_classes:
                logger.error(f"Connector type {connector_type.value} not registered")
                return False
            
            # Create connector instance
            connector_class = self._connector_classes[connector_type]
            connector = connector_class(connector_type, connector_type.value)
            
            # Initialize the connector
            if not connector.initialize(config):
                logger.error(f"Failed to initialize connector {connector_type.value}")
                return False
            
            # Create service instance
            service_class = self._service_classes[connector_type]
            service = service_class(connector)
            
            # Create gRPC service instance
            grpc_class = self._grpc_classes[connector_type]
            grpc_service = grpc_class(service)
            
            # Store instances
            self._connectors[connector_type] = connector
            self._connector_services[connector_type] = service
            self._grpc_services[connector_type] = grpc_service
            
            logger.info(f"Successfully initialized connector: {connector_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize connector {connector_type.value}: {str(e)}")
            return False
    
    def get_connector(self, connector_type: ConnectorType) -> Optional[BaseConnector]:
        """Get a connector instance by type."""
        return self._connectors.get(connector_type)
    
    def get_service(self, connector_type: ConnectorType) -> Optional[BaseConnectorService]:
        """Get a connector service instance by type."""
        return self._connector_services.get(connector_type)
    
    def get_grpc_service(self, connector_type: ConnectorType) -> Optional[BaseConnectorGrpcService]:
        """Get a connector gRPC service instance by type."""
        return self._grpc_services.get(connector_type)
    
    def get_all_connectors(self) -> Dict[ConnectorType, BaseConnector]:
        """Get all registered connector instances."""
        return self._connectors.copy()
    
    def get_available_connector_types(self) -> List[ConnectorType]:
        """Get list of all registered connector types."""
        return list(self._connector_classes.keys())
    
    def is_connector_registered(self, connector_type: ConnectorType) -> bool:
        """Check if a connector type is registered."""
        return connector_type in self._connector_classes
    
    def is_connector_initialized(self, connector_type: ConnectorType) -> bool:
        """Check if a connector is initialized."""
        return connector_type in self._connectors
    
    def shutdown_connector(self, connector_type: ConnectorType) -> bool:
        """
        Shutdown a specific connector.
        
        Args:
            connector_type: The type of connector to shutdown
            
        Returns:
            Boolean indicating if shutdown was successful
        """
        try:
            if connector_type in self._connectors:
                del self._connectors[connector_type]
            if connector_type in self._connector_services:
                del self._connector_services[connector_type]
            if connector_type in self._grpc_services:
                del self._grpc_services[connector_type]
            
            logger.info(f"Successfully shutdown connector: {connector_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to shutdown connector {connector_type.value}: {str(e)}")
            return False
    
    def shutdown_all(self):
        """Shutdown all connectors."""
        for connector_type in list(self._connectors.keys()):
            self.shutdown_connector(connector_type)


# Global registry instance
connector_registry = ConnectorRegistry()


def get_connector_registry() -> ConnectorRegistry:
    """Get the global connector registry instance."""
    return connector_registry
