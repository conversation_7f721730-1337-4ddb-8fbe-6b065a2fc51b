"""
Google Drive Connector Service

This module implements the Google Drive connector service using the base connector framework,
providing the service layer for Google Drive operations while using the independent search service.
"""

from typing import Dict, Any, List, <PERSON><PERSON>
import structlog
from app.modules.connectors.base.connector import BaseConnectorService
from app.modules.connectors.google_drive.connector import GoogleDriveConnector
from app.services.context_search_service import ContextSearchService

logger = structlog.get_logger()


class GoogleDriveConnectorService(BaseConnectorService):
    """
    Google Drive connector service implementation.
    
    This service provides the business logic layer for Google Drive operations
    while delegating search functionality to the independent context search service.
    """
    
    def __init__(self, connector: GoogleDriveConnector):
        """
        Initialize the Google Drive connector service.
        
        Args:
            connector: The Google Drive connector instance
        """
        super().__init__(connector)
        self.drive_service = connector.get_drive_service()
        self.context_search_service = ContextSearchService()
    
    def sync_file_by_id(self, file_id: str, agent_id: str, user_id: str, 
                       organisation_id: str, **kwargs) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Sync a specific file by its ID.
        
        Args:
            file_id: ID of the file to sync
            agent_id: ID of the agent
            user_id: ID of the user
            organisation_id: ID of the organization
            **kwargs: Additional parameters (e.g., url for generic files)
            
        Returns:
            Tuple containing success status, message, and file data
        """
        try:
            # Use the existing drive service sync functionality
            return self.drive_service.sync_file_by_id(
                file_id=file_id,
                agent_id=agent_id,
                user_id=user_id,
                organisation_id=organisation_id,
                url=kwargs.get('url')
            )
            
        except Exception as e:
            logger.error(f"Failed to sync file {file_id}: {str(e)}")
            return False, f"Sync failed: {str(e)}", {}
    
    def get_file_metadata(self, file_id: str, organisation_id: str) -> Dict[str, Any]:
        """
        Get metadata for a specific file.
        
        Args:
            file_id: ID of the file
            organisation_id: ID of the organization
            
        Returns:
            Dictionary containing file metadata
        """
        try:
            # Use the existing drive service to get file metadata
            success, message, file_data = self.drive_service.get_file_metadata(file_id, organisation_id)
            
            if success:
                return file_data
            else:
                logger.error(f"Failed to get file metadata: {message}")
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get file metadata for {file_id}: {str(e)}")
            return {}
    
    def list_files(self, organisation_id: str, limit: int = 100, 
                  offset: int = 0) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        List files from Google Drive.
        
        Args:
            organisation_id: ID of the organization
            limit: Maximum number of files to return
            offset: Number of files to skip
            
        Returns:
            Tuple containing success status, message, and list of files
        """
        try:
            # Use the existing drive service to list files
            return self.drive_service.list_files(
                organisation_id=organisation_id,
                limit=limit,
                offset=offset
            )
            
        except Exception as e:
            logger.error(f"Failed to list files: {str(e)}")
            return False, f"List files failed: {str(e)}", []
    
    def search_similar_documents(self, user_id: str, query_text: str, top_k: int = 5,
                               agent_id: str = None, organisation_id: str = None,
                               file_ids: List[str] = None) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        Search for similar documents using the independent context search service.
        
        This method delegates to the context search service instead of implementing
        search functionality directly in the connector.
        
        Args:
            user_id: The ID of the user
            query_text: The query text to search for
            top_k: The number of results to return
            agent_id: Optional ID of the agent
            organisation_id: ID of the organization
            file_ids: Optional list of specific file IDs to search within
            
        Returns:
            Tuple containing success status, message, and search results
        """
        try:
            # Filter to only Google Drive files by specifying connector type
            return self.context_search_service.search_similar_documents(
                user_id=user_id,
                query_text=query_text,
                top_k=top_k,
                agent_id=agent_id,
                organisation_id=organisation_id,
                file_ids=file_ids,
                connector_types=['google_drive']  # Only search Google Drive files
            )
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            return False, f"Search failed: {str(e)}", []
    
    def batch_search_similar_documents(self, user_id: str, query_texts: List[str], top_k: int = 5,
                                     agent_id: str = None, organisation_id: str = None,
                                     file_ids: List[str] = None) -> Tuple[bool, str, List[List[Dict[str, Any]]]]:
        """
        Perform batch search using the independent context search service.
        
        Args:
            user_id: The ID of the user
            query_texts: List of query texts to search for
            top_k: The number of results to return for each query
            agent_id: Optional ID of the agent
            organisation_id: ID of the organization
            file_ids: Optional list of specific file IDs to search within
            
        Returns:
            Tuple containing success status, message, and batch search results
        """
        try:
            # Filter to only Google Drive files by specifying connector type
            return self.context_search_service.batch_search_similar_documents(
                user_id=user_id,
                query_texts=query_texts,
                top_k=top_k,
                agent_id=agent_id,
                organisation_id=organisation_id,
                file_ids=file_ids,
                connector_types=['google_drive']  # Only search Google Drive files
            )
            
        except Exception as e:
            logger.error(f"Batch search failed: {str(e)}")
            return False, f"Batch search failed: {str(e)}", []
