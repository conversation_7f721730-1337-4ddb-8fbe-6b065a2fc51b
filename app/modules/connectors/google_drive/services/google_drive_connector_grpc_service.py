"""
Google Drive Connector gRPC Service

This module implements the Google Drive gRPC service using the base connector framework,
providing the gRPC interface while using the independent search service.
"""

import grpc
import structlog
from typing import Dict, Any
from app.grpc_ import google_drive_pb2, google_drive_pb2_grpc
from app.modules.connectors.base.connector import BaseConnectorGrpcService
from app.modules.connectors.google_drive.services.google_drive_connector_service import GoogleDriveConnectorService
from app.modules.organisation.services.organisation import OrganisationService

logger = structlog.get_logger()


class GoogleDriveConnectorGrpcService(BaseConnectorGrpcService, google_drive_pb2_grpc.GoogleDriveServiceServicer):
    """
    Google Drive gRPC service implementation using the connector framework.
    
    This service provides the gRPC interface for Google Drive operations
    while using the independent search service for context retrieval.
    """
    
    def __init__(self, connector_service: GoogleDriveConnectorService):
        """
        Initialize the gRPC service.
        
        Args:
            connector_service: The Google Drive connector service instance
        """
        super().__init__(connector_service)
        self.organisation_service = OrganisationService()
    
    def sync_data(self, request, context):
        """Handle sync data gRPC request."""
        return self.syncDrive(request, context)
    
    def get_sync_status(self, request, context):
        """Handle get sync status gRPC request."""
        return self.getSyncStatus(request, context)
    
    def disconnect(self, request, context):
        """Handle disconnect gRPC request."""
        return self.disconnectDrive(request, context)
    
    def syncDrive(self, request, context):
        """
        Sync Google Drive data for an organization.
        """
        logger.info("Received request to sync Google Drive", 
                   organisation_id=request.organisation_id,
                   full_sync=request.full_sync)
        
        try:
            # Validate organisation_id is provided
            if not request.organisation_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("organisation_id is required")
                return google_drive_pb2.SyncDriveResponse(
                    success=False,
                    message="organisation_id is required",
                    sync_status="failed"
                )
            
            # Use the connector to sync data
            success, message = self.connector_service.connector.sync_data(
                user_id=request.organisation_id,  # Using organisation_id as user_id for scheduling
                organisation_id=request.organisation_id,
                full_sync=request.full_sync
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.SyncDriveResponse(
                    success=False,
                    message=message,
                    sync_status="failed"
                )
            
            return google_drive_pb2.SyncDriveResponse(
                success=True,
                message=message,
                sync_status="scheduled"
            )
            
        except Exception as e:
            logger.error(f"Error in syncDrive: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return google_drive_pb2.SyncDriveResponse(
                success=False,
                message=str(e),
                sync_status="failed"
            )
    
    def getSyncStatus(self, request, context):
        """
        Get sync status for Google Drive.
        """
        logger.info("Received request to get Google Drive sync status", 
                   organisation_id=request.organisation_id)
        
        try:
            # Get sync status from the connector
            status_info = self.connector_service.connector.get_sync_status(request.organisation_id)
            
            return google_drive_pb2.SyncStatusResponse(
                success=True,
                sync_status=status_info.get('status', 'unknown'),
                message=status_info.get('message', 'Status retrieved'),
                last_sync_time=status_info.get('last_sync', ''),
                files_synced=status_info.get('files_synced', 0)
            )
            
        except Exception as e:
            logger.error(f"Error in getSyncStatus: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return google_drive_pb2.SyncStatusResponse(
                success=False,
                sync_status="error",
                message=str(e)
            )
    
    def disconnectDrive(self, request, context):
        """
        Disconnect Google Drive for an organization.
        """
        logger.info("Received request to disconnect Google Drive", 
                   organisation_id=request.organisation_id)
        
        try:
            # Use the connector to disconnect
            success, message = self.connector_service.connector.disconnect(request.organisation_id)
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.DisconnectDriveResponse(success=False, message=message)
            
            return google_drive_pb2.DisconnectDriveResponse(
                success=True,
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error in disconnectDrive: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return google_drive_pb2.DisconnectDriveResponse(success=False, message=str(e))
    
    def searchSimilarDocuments(self, request, context):
        """
        Search for similar documents using the independent context search service.
        """
        logger.info("Received request to search similar documents",
                   user_id=request.user_id,
                   query_text=request.query_text[:50] + "..." if len(request.query_text) > 50 else request.query_text,
                   top_k=request.top_k)
        
        try:
            # Extract file_ids from request if provided
            file_ids = None
            if hasattr(request, 'file_ids') and request.file_ids:
                file_ids = list(request.file_ids)
                logger.info(f"Filtering search to {len(file_ids)} specific files")
            
            # Use the connector service's search method (which uses the independent search service)
            success, message, results = self.connector_service.search_similar_documents(
                request.user_id,
                request.query_text,
                request.top_k if request.top_k > 0 else 5,
                request.agent_id if hasattr(request, 'agent_id') and request.agent_id else None,
                request.organisation_id if hasattr(request, 'organisation_id') and request.organisation_id else None,
                file_ids
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.SearchSimilarDocumentsResponse(success=False)
            
            # Convert to proto models
            result_models = []
            for result in results:
                result_model = google_drive_pb2.SearchResultItem(
                    file_id=result['file_id'],
                    file_name=result['file_name'],
                    mime_type=result.get('mime_type', ''),
                    web_view_link=result.get('web_view_link', ''),
                    created_time=result['created_time'],
                    modified_time=result['modified_time'],
                    score=float(result['score']),
                    vector_id=result['vector_id'],
                    chunk_text=result.get('chunk_text', '')
                )
                result_models.append(result_model)
            
            return google_drive_pb2.SearchSimilarDocumentsResponse(
                success=True,
                message=message,
                results=result_models
            )
            
        except Exception as e:
            logger.error(f"Error in searchSimilarDocuments: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return google_drive_pb2.SearchSimilarDocumentsResponse(success=False)
    
    def batchSearchSimilarDocuments(self, request, context):
        """
        Perform batch search for similar documents using the independent context search service.
        """
        logger.info("Received request for batch search similar documents",
                   user_id=request.user_id,
                   num_queries=len(request.query_texts),
                   top_k=request.top_k)
        
        try:
            # Extract file_ids from request if provided
            file_ids = None
            if hasattr(request, 'file_ids') and request.file_ids:
                file_ids = list(request.file_ids)
                logger.info(f"Filtering batch search to {len(file_ids)} specific files")
            
            # Use the connector service's batch search method
            success, message, all_results = self.connector_service.batch_search_similar_documents(
                request.user_id,
                list(request.query_texts),
                request.top_k if request.top_k > 0 else 5,
                request.agent_id if hasattr(request, 'agent_id') and request.agent_id else None,
                request.organisation_id if hasattr(request, 'organisation_id') and request.organisation_id else None,
                file_ids
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.BatchSearchSimilarDocumentsResponse(success=False)
            
            # Convert results to proto models
            query_results_list = []
            for i, (query_text, results) in enumerate(zip(request.query_texts, all_results)):
                result_models = []
                for result in results:
                    result_model = google_drive_pb2.SearchResultItem(
                        file_id=result['file_id'],
                        file_name=result['file_name'],
                        mime_type=result.get('mime_type', ''),
                        web_view_link=result.get('web_view_link', ''),
                        created_time=result['created_time'],
                        modified_time=result['modified_time'],
                        score=float(result['score']),
                        vector_id=result['vector_id'],
                        chunk_text=result.get('chunk_text', '')
                    )
                    result_models.append(result_model)
                
                query_results = google_drive_pb2.QueryResults(
                    query_text=query_text,
                    results=result_models
                )
                query_results_list.append(query_results)
            
            return google_drive_pb2.BatchSearchSimilarDocumentsResponse(
                success=True,
                message=message,
                query_results=query_results_list
            )
            
        except Exception as e:
            logger.error(f"Error in batchSearchSimilarDocuments: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return google_drive_pb2.BatchSearchSimilarDocumentsResponse(success=False)
    
    def syncFileByUrl(self, request, context):
        """
        Sync a file by URL using the connector service.
        """
        logger.info("Received request to sync file by URL",
                   drive_url=request.drive_url,
                   agent_id=request.agent_id,
                   organisation_id=request.organisation_id)
        
        try:
            # Extract file ID from URL or use URL directly for generic files
            file_id = None
            if "drive.google.com" in request.drive_url:
                # Extract Google Drive file ID from URL
                import re
                match = re.search(r'/d/([a-zA-Z0-9-_]+)', request.drive_url)
                if match:
                    file_id = match.group(1)
                else:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid Google Drive URL format")
                    return google_drive_pb2.SyncFileByUrlResponse(
                        success=False,
                        message="Invalid Google Drive URL format",
                        sync_status="failed"
                    )
            else:
                # Handle generic HTTP/HTTPS URL
                import uuid
                file_id = str(uuid.uuid4())
            
            # Use the connector service to sync the file
            success, message, file_data = self.connector_service.sync_file_by_id(
                file_id,
                request.agent_id,
                request.user_id if hasattr(request, 'user_id') and request.user_id else None,
                request.organisation_id,
                url=request.drive_url if "drive.google.com" not in request.drive_url else None
            )
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return google_drive_pb2.SyncFileByUrlResponse(
                    success=False,
                    message=message,
                    sync_status="failed"
                )
            
            return google_drive_pb2.SyncFileByUrlResponse(
                success=True,
                message=message,
                file_id=file_data['id'],
                file_name=file_data['name'],
                sync_status="completed"
            )
            
        except Exception as e:
            logger.error(f"Error in syncFileByUrl: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return google_drive_pb2.SyncFileByUrlResponse(
                success=False,
                message=str(e),
                sync_status="failed"
            )
