import json
import time
import signal
import threading
from datetime import datetime
import structlog

from app.utils.redis.redis_service import RedisService
from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService

logger = structlog.get_logger()

class GoogleDriveSyncWorker:
    """
    Worker to process Google Drive sync jobs from Redis.
    """
    
    def __init__(self, poll_interval=5):
        """
        Initialize the worker.
        
        Args:
            poll_interval: Time in seconds between polling Redis for new jobs
        """
        self.redis_service = RedisService()
        self.drive_service = GoogleDriveService()
        self.poll_interval = poll_interval
        self.running = False
        self.thread = None
    
    def start(self):
        """
        Start the worker in a background thread.
        """
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info("Google Drive sync worker started")
    
    def stop(self):
        """
        Stop the worker.
        """
        self.running = False
        if self.thread:
            self.thread.join(timeout=30)
        logger.info("Google Drive sync worker stopped")
    
    def _run(self):
        """
        Main worker loop.
        """
        while self.running:
            try:
                # Get the next job from the sorted set
                jobs = self.redis_service.zrangebyscore(
                    "gdrive_sync_queue", 
                    "-inf", 
                    time.time(),
                    start=0,
                    num=1
                )
                if not jobs:
                    # No jobs ready yet, sleep and try again
                    time.sleep(self.poll_interval)
                    continue
                
                job_id = jobs[0]
                
                # Remove the job from the queue
                self.redis_service.zrem("gdrive_sync_queue", job_id)
                
                # Get the job data
                job_data_str = self.redis_service.get(job_id)
                if not job_data_str:
                    logger.warning(f"Job {job_id} not found in Redis")
                    continue
                
                # Parse job data
                job_data = json.loads(job_data_str)
                user_id = job_data.get('user_id')
                organisation_id = job_data.get('organisation_id')
                full_sync = job_data.get('full_sync', False)
                
                if not user_id or not organisation_id:
                    logger.error(f"Invalid job data: {job_data}")
                    continue
                
                # Process the job
                logger.info(f"Processing Google Drive sync job for organization {organisation_id}")
                success, message, files_synced, folders_synced = self.drive_service.sync_drive(
                    organisation_id, full_sync
                )
                
                # Log the result
                if success:
                    logger.info(f"Google Drive sync completed for user {user_id}: {files_synced} files, {folders_synced} folders")
                else:
                    logger.error(f"Google Drive sync failed for user {user_id}: {message}")
                
                # Delete the job
                self.redis_service.delete(job_id)
                
            except Exception as e:
                logger.error(f"Error processing Google Drive sync job: {str(e)}")
                time.sleep(self.poll_interval)
    
    def process_job_now(self, user_id, organisation_id, full_sync=False):
        """
        Process a sync job immediately.
        
        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organization
            full_sync: Whether to perform a full sync
            
        Returns:
            Tuple containing:
            - success: Boolean indicating if sync was successful
            - message: Status message
            - files_synced: Number of files synced
            - folders_synced: Number of folders synced
        """
        try:
            logger.info(f"Processing immediate Google Drive sync job for organization {organisation_id}")
            return self.drive_service.sync_drive(organisation_id, full_sync)
        except Exception as e:
            logger.error(f"Error processing immediate Google Drive sync job: {str(e)}")
            return False, f"Error processing sync job: {str(e)}", 0, 0