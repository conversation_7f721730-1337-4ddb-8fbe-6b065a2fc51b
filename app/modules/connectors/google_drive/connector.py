"""
Google Drive Connector Implementation

This module implements the Google Drive connector using the base connector framework,
making it compatible with the standardized connector system.
"""

from typing import Dict, Any, List, Tuple
import structlog
from app.modules.connectors.base.connector import BaseConnector, ConnectorType, ConnectorStatus
from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
from app.utils.redis.redis_service import RedisService

logger = structlog.get_logger()


class GoogleDriveConnector(BaseConnector):
    """
    Google Drive connector implementation.
    
    This connector handles Google Drive integration following the standardized
    connector framework.
    """
    
    def __init__(self):
        """Initialize the Google Drive connector."""
        super().__init__(ConnectorType.GOOGLE_DRIVE, "Google Drive")
        self.drive_service = None
        self.redis_service = None
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """
        Initialize the Google Drive connector with configuration.
        
        Args:
            config: Configuration dictionary containing Google Drive settings
            
        Returns:
            Boolean indicating if initialization was successful
        """
        try:
            # Initialize the underlying Google Drive service
            self.drive_service = GoogleDriveService()
            self.redis_service = RedisService()
            
            # Test the services
            if not self.drive_service.pinecone_service.is_initialized():
                logger.error("Pinecone service not initialized for Google Drive connector")
                return False
            
            self._initialized = True
            self.set_status(ConnectorStatus.ACTIVE)
            logger.info("Google Drive connector initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Google Drive connector: {str(e)}")
            self.set_status(ConnectorStatus.ERROR)
            return False
    
    def test_connection(self) -> Tuple[bool, str]:
        """
        Test the connection to Google Drive.
        
        Returns:
            Tuple containing success status and message
        """
        if not self._initialized:
            return False, "Connector not initialized"
        
        try:
            # Test if the underlying services are working
            if not self.drive_service.pinecone_service.is_initialized():
                return False, "Pinecone service not available"
            
            return True, "Google Drive connector is ready"
            
        except Exception as e:
            logger.error(f"Google Drive connection test failed: {str(e)}")
            return False, f"Connection test failed: {str(e)}"
    
    def sync_data(self, user_id: str, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str]:
        """
        Sync data from Google Drive.
        
        Args:
            user_id: ID of the user initiating the sync
            organisation_id: ID of the organization
            full_sync: Whether to perform a full sync or incremental
            
        Returns:
            Tuple containing success status and message
        """
        if not self._initialized:
            return False, "Connector not initialized"
        
        try:
            self.set_status(ConnectorStatus.SYNCING)
            
            # Schedule sync job using the existing service
            job_id = self.drive_service._schedule_sync(
                user_id=user_id,
                organisation_id=organisation_id,
                full_sync=full_sync,
                delay_seconds=0
            )
            
            if job_id:
                message = f"Google Drive sync scheduled successfully (Job ID: {job_id})"
                logger.info(message)
                self.set_status(ConnectorStatus.ACTIVE)
                return True, message
            else:
                self.set_status(ConnectorStatus.ERROR)
                return False, "Failed to schedule Google Drive sync"
                
        except Exception as e:
            logger.error(f"Google Drive sync failed: {str(e)}")
            self.set_status(ConnectorStatus.ERROR)
            return False, f"Sync failed: {str(e)}"
    
    def disconnect(self, organisation_id: str) -> Tuple[bool, str]:
        """
        Disconnect and clean up Google Drive data.
        
        Args:
            organisation_id: ID of the organization
            
        Returns:
            Tuple containing success status and message
        """
        if not self._initialized:
            return False, "Connector not initialized"
        
        try:
            # Use the existing disconnect functionality
            success, message = self.drive_service.disconnect_drive(organisation_id)
            
            if success:
                self.set_status(ConnectorStatus.DISCONNECTED)
                logger.info(f"Google Drive disconnected for organization {organisation_id}")
            else:
                self.set_status(ConnectorStatus.ERROR)
                logger.error(f"Failed to disconnect Google Drive: {message}")
            
            return success, message
            
        except Exception as e:
            logger.error(f"Google Drive disconnect failed: {str(e)}")
            self.set_status(ConnectorStatus.ERROR)
            return False, f"Disconnect failed: {str(e)}"
    
    def get_sync_status(self, organisation_id: str) -> Dict[str, Any]:
        """
        Get the current sync status for an organization.
        
        Args:
            organisation_id: ID of the organization
            
        Returns:
            Dictionary containing sync status information
        """
        if not self._initialized:
            return {
                'status': 'not_initialized',
                'message': 'Connector not initialized',
                'last_sync': None,
                'files_synced': 0
            }
        
        try:
            # Get sync status from the existing service
            # This is a placeholder - the actual implementation would query
            # the database or Redis for sync status
            return {
                'status': self.status.value,
                'message': 'Google Drive connector operational',
                'last_sync': None,  # Would be populated from database
                'files_synced': 0,  # Would be populated from database
                'connector_type': self.connector_type.value
            }
            
        except Exception as e:
            logger.error(f"Failed to get Google Drive sync status: {str(e)}")
            return {
                'status': 'error',
                'message': f"Failed to get sync status: {str(e)}",
                'last_sync': None,
                'files_synced': 0
            }
    
    def get_supported_file_types(self) -> List[str]:
        """
        Get list of file types supported by Google Drive connector.
        
        Returns:
            List of supported MIME types
        """
        return [
            'application/pdf',
            'application/vnd.google-apps.document',
            'application/vnd.google-apps.spreadsheet',
            'application/vnd.google-apps.presentation',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
            'application/json',
            'text/html',
            'application/rtf'
        ]
    
    def get_drive_service(self) -> GoogleDriveService:
        """
        Get the underlying Google Drive service instance.
        
        Returns:
            GoogleDriveService instance
        """
        return self.drive_service
