from app.modules.agents.models.schema_loader import agent_schema

class AgentQueries:
    """Queries for agent management operations."""
    
    def __init__(self):
        # Get schema definitions
        self.user_label = "User"  # From organisation schema
        self.dept_label = "Department"  # From organisation schema
        self.org_label = "Organisation"  # From organisation schema
        self.agent_label = agent_schema.get_node_labels()[0]  # "Agent"
        
        # Relationship types
        self.owns_rel = agent_schema.get_relationship_types()[0]  # "OWNS"
        self.belongs_to_rel = agent_schema.get_relationship_types()[1]  # "BELONGS_TO"
        self.has_access_rel = agent_schema.get_relationship_types()[2]  # "HAS_ACCESS"
        
        # Organisation relationships (from organisation schema)
        self.has_dept_rel = "HAS_DEPARTMENT"  # Organisation to Department

    @property
    def VALIDATE_OWNER_EXISTS(self):
        """Validate that the owner user exists."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        RETURN u
        """

    @property
    def VALIDATE_DEPARTMENT_EXISTS(self):
        """Validate that the department exists."""
        return f"""
        MATCH (d:{self.dept_label} {{id: $dept_id}})
        RETURN d
        """

    @property
    def VALIDATE_OWNER_DEPARTMENT_ORG(self):
        """Validate that owner and department belong to the same organization."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.belongs_to_rel}]->(d:{self.dept_label} {{name: $gen_dept}})
        MATCH (o1:{self.org_label})-[:{self.has_dept_rel}]->(d)
        MATCH (org:{self.org_label})-[:{self.has_dept_rel}]->(:{self.dept_label} {{id: $dept_id}})
        WHERE o1.id = org.id
        RETURN o1.id AS organisation_id
        """

    @property
    def VALIDATE_USERS_EXIST(self):
        """Validate that all specified users exist."""
        return f"""
        MATCH (u:{self.user_label})
        WHERE u.id IN $user_ids
        RETURN collect(u.id) as existing_users
        """

    @property
    def CREATE_AGENT(self):
        """Create a new agent node."""
        return f"""
        CREATE (a:{self.agent_label} {{
            id: $id,
            name: $name,
            description: $description,
            department_id: $department_id,
            owner_id: $owner_id,
            user_ids: $user_ids,
            created_at: $created_at,
            updated_at: $updated_at,
            visibility: $visibility,
            status: $status,
            creator_role: $creator_role
        }})
        RETURN a
        """

    @property
    def GET_AGENT_WITH_RELATIONSHIPS(self):
        """Get agent with all its relationships."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $id}})
        OPTIONAL MATCH (a)-[:{self.belongs_to_rel}]->(d:{self.dept_label})
        OPTIONAL MATCH (u:{self.user_label})-[:{self.owns_rel}]->(a)
        OPTIONAL MATCH (users:{self.user_label})-[:{self.has_access_rel}]->(a)
        RETURN a, d, u, collect(distinct users) as access_users
        """

    @property
    def LIST_AGENTS_BY_USER(self):
        """List all agents that a user has access to."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(a:{self.agent_label})
        OPTIONAL MATCH (a)-[:{self.belongs_to_rel}]->(d:{self.dept_label})
        OPTIONAL MATCH (owner:{self.user_label})-[:{self.owns_rel}]->(a)
        RETURN a, d, owner
        """

    @property
    def LIST_AGENTS_BY_ORGANIZATION(self):
        """List all agents from all departments of an organization."""
        return f"""
        MATCH (o:{self.org_label} {{id: $org_id}})-[:{self.has_dept_rel}]->(d:{self.dept_label})<-[:{self.belongs_to_rel}]-(a:{self.agent_label})
        OPTIONAL MATCH (owner:{self.user_label})-[:{self.owns_rel}]->(a)
        RETURN a, d, owner
        """

    @property
    def LIST_AGENTS_BY_DEPARTMENT(self):
        """List all agents from a specific department."""
        return f"""
        MATCH (d:{self.dept_label} {{id: $dept_id}})<-[:{self.belongs_to_rel}]-(a:{self.agent_label})
        OPTIONAL MATCH (owner:{self.user_label})-[:{self.owns_rel}]->(a)
        RETURN a, owner
        """

    @property
    def CHECK_USER_AGENT_ACCESS(self):
        """Check if a user has access to a specific agent."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[access:{self.has_access_rel}]->(a:{self.agent_label} {{id: $agent_id}})
        RETURN access
        """

    @property
    def UPDATE_AGENT(self):
        """Update agent properties."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $id}})
        SET a.name = $name,
            a.description = $description,
            a.updated_at = $updated_at,
            a.visibility = $visibility,
            a.status = $status
        RETURN a
        """

    @property
    def DELETE_AGENT(self):
        """Delete an agent and all its relationships."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $id}})
        DETACH DELETE a
        """

    @property
    def GET_AGENT_OWNER(self):
        """Get the owner of an agent."""
        return f"""
        MATCH (u:{self.user_label})-[:{self.owns_rel}]->(a:{self.agent_label} {{id: $agent_id}})
        RETURN u
        """

    @property
    def GET_AGENT_DEPARTMENT(self):
        """Get the department of an agent."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})-[:{self.belongs_to_rel}]->(d:{self.dept_label})
        RETURN d
        """

    @property
    def COUNT_AGENTS_BY_DEPARTMENT(self):
        """Count agents in a department."""
        return f"""
        MATCH (d:{self.dept_label} {{id: $dept_id}})<-[:{self.belongs_to_rel}]-(a:{self.agent_label})
        RETURN count(a) as agent_count
        """

    @property
    def COUNT_AGENTS_BY_ORGANIZATION(self):
        """Count agents in an organization."""
        return f"""
        MATCH (o:{self.org_label} {{id: $org_id}})-[:{self.has_dept_rel}]->(d:{self.dept_label})<-[:{self.belongs_to_rel}]-(a:{self.agent_label})
        RETURN count(a) as agent_count
        """