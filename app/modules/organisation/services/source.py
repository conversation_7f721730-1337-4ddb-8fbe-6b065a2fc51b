import uuid
import json
from datetime import datetime
import structlog
import grpc

from app.db.neo4j import execute_write_query, execute_read_query
from app.modules.organisation.repository.source import SourceQueries
from app.grpc_ import organisation_pb2
from app.utils.constants.sources import SourceType
from app.utils.constants.departments import DefaultDepartments
from app.utils.source_credentials import detect_credential_type
from app.utils.google_service_account import GoogleServiceAccountManager

logger = structlog.get_logger()

class SourceService:
    """Service handling source operations."""
    
    def __init__(self):
        self.queries = SourceQueries()
        self.google_sa_manager = GoogleServiceAccountManager()

    def addSource(self, request, context):
        """
        Add a new source with credentials to an organization.
        Supports both OAuth credentials and service account keys.

        Args:
            request: Contains organisation_id, type, name, credentials_file, and service_account_key
            context: gRPC context

        Returns:
            Response indicating success/failure and the created source
        """
        logger.info("Received request to add source",
                   org_id=request.organisation_id,
                   source_type=request.type)

        try:
            # Validate that service account key is provided
            if not request.service_account_key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Service account key must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Service account key must be provided"
                )

            # Validate JSON format for service account key
            try:
                json.loads(request.service_account_key)
                service_account_key = request.service_account_key
            except json.JSONDecodeError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in service_account_key")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Invalid JSON format in service_account_key"
                )

            # Map protobuf enum to database value
            source_type_mapping = {
                organisation_pb2.SourceType.GOOGLE_DRIVE: SourceType.GOOGLE_DRIVE.value,
                organisation_pb2.SourceType.SLACK: SourceType.SLACK.value
            }
            
            if request.type not in source_type_mapping:
                logger.error(f"Unsupported source type: {request.type}")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Unsupported source type: {request.type}")
                return organisation_pb2.AddSourceResponse(success=False, message=f"Unsupported source type: {request.type}")
            
            db_source_type = source_type_mapping[request.type]

            # Check if a source of this type already exists for the organization
            existing_query = self.queries.CHECK_EXISTING_SOURCE
            existing_params = {
                "org_id": request.organisation_id,
                "source_type": db_source_type
            }

            existing_result = execute_read_query(existing_query, existing_params)

            if existing_result:
                logger.error(f"Source of type {db_source_type} already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Source of type {db_source_type} already exists for this organization")
                return organisation_pb2.AddSourceResponse(success=False, message=f"Source of type {db_source_type} already exists for this organization")

            # Validate service account if provided
            validation_success = True
            validation_message = "Source added successfully"
            accessible_folders = []
            
            if service_account_key and db_source_type == SourceType.GOOGLE_DRIVE.value:
                validation_success, validation_message, accessible_folders = \
                    self.google_sa_manager.validate_service_account_access(service_account_key)
                
                if not validation_success:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Service account validation failed: {validation_message}")
                    return organisation_pb2.AddSourceResponse(
                        success=False,
                        message=f"Service account validation failed: {validation_message}"
                    )

            # Generate a unique ID for the source
            source_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()

            # Create the source node in Neo4j using repository
            query = self.queries.CREATE_SOURCE
            params = {
                "org_id": request.organisation_id,
                "id": source_id,
                "name": request.name,
                "type": db_source_type,
                "service_account_key": service_account_key,
                "is_validated": validation_success,
                "validation_message": validation_message,
                "last_validated_at": current_time if validation_success else None,
                "created_at": current_time,
                "updated_at": current_time
            }

            result = execute_write_query(query, params)

            if not result:
                logger.error("Failed to create source in Neo4j")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create source")
                return organisation_pb2.AddSourceResponse(success=False, message="Failed to create source")

            # If service account validation was successful and we have folders, create folder nodes
            if (service_account_key and
                validation_success and
                accessible_folders and
                db_source_type == SourceType.GOOGLE_DRIVE.value):
                
                from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
                gdrive_service = GoogleDriveService()
                
                # Extract file_ids from request if provided
                file_ids = list(request.file_ids) if request.file_ids else None
                
                folder_creation_success, synced_files = gdrive_service.create_folder_nodes_with_permissions(
                    request.organisation_id, accessible_folders, file_ids
                )
                
                if folder_creation_success:
                    if file_ids:
                        validation_message += f" Created {len(accessible_folders)} folder nodes and synced {len(synced_files)} specific files with permission-based access."
                    else:
                        validation_message += f" Created {len(accessible_folders)} folder nodes with permission-based access."

            # Schedule automatic sync for Google Drive sources after successful creation
            if db_source_type == SourceType.GOOGLE_DRIVE.value and validation_success:
                try:
                    # Schedule an immediate sync job for the newly added Google Drive source
                    job_id = gdrive_service._schedule_sync(
                        user_id=request.organisation_id,  # Using organisation_id as user_id for scheduling
                        organisation_id=request.organisation_id,
                        full_sync=True,  # Full sync for new sources
                        delay_seconds=5  # Small delay to ensure source is fully created
                    )
                    
                    logger.info(f"Automatic sync job scheduled for new Google Drive source",
                              organisation_id=request.organisation_id,
                              job_id=job_id)
                    validation_message += f" Automatic sync job scheduled (ID: {job_id})."
                    
                except Exception as sync_error:
                    logger.warning(f"Failed to schedule automatic sync for new source: {str(sync_error)}")
                    validation_message += " Note: Automatic sync scheduling failed, manual sync may be required."

            # Create source model for response
            source_model = organisation_pb2.SourceModel(
                id=source_id,
                organisation_id=request.organisation_id,
                type=request.type,
                name=request.name,
                created_at=current_time,
                updated_at=current_time
            )
            
            # Create file info objects for synced files
            file_info_objects = []
            if service_account_key and validation_success and 'synced_files' in locals():
                for file_data in synced_files:
                    file_info = organisation_pb2.FileInfo(
                        id=file_data['id'],
                        name=file_data['name']
                    )
                    file_info_objects.append(file_info)

            return organisation_pb2.AddSourceResponse(
                success=True,
                message=validation_message,
                source=source_model,
                synced_files=file_info_objects
            )

        except Exception as e:
            logger.error("Error adding source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error adding source: {str(e)}")
            return organisation_pb2.AddSourceResponse(success=False, message=f"Error adding source: {str(e)}")

    def listSources(self, request, context):
        """
        List all sources for an organization.

        Args:
            request: Contains organisation_id
            context: gRPC context

        Returns:
            Response containing a list of sources and isInitialMapping flag
        """
        logger.info("Received request to list sources", org_id=request.organisation_id)

        try:
            query = self.queries.LIST_SOURCES
            params = {
                "org_id": request.organisation_id
            }

            result = execute_read_query(query, params)

            sources = []
            for record in result:
                source = record.get('s', {})
                
                # Map database source type to protobuf enum
                db_source_type = source.get('type', SourceType.GOOGLE_DRIVE.value)
                
                db_to_proto_mapping = {
                    SourceType.GOOGLE_DRIVE.value: organisation_pb2.SourceType.GOOGLE_DRIVE,
                    SourceType.SLACK.value: organisation_pb2.SourceType.SLACK
                }
                
                proto_source_type = db_to_proto_mapping.get(db_source_type, organisation_pb2.SourceType.GOOGLE_DRIVE)

                source_model = organisation_pb2.SourceModel(
                    id=source.get('id'),
                    organisation_id=request.organisation_id,
                    type=proto_source_type,
                    name=source.get('name'),
                    created_at=source.get('created_at'),
                    updated_at=source.get('updated_at')
                )
                sources.append(source_model)
            
            # Check if any department other than general has access to folders
            is_initial_mapping_query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_DEPARTMENT]->(d:Department)
            WHERE NOT toLower(d.name) = toLower($general_dept)
            MATCH (d)-[:HAS_ACCESS]->(f:GoogleDriveFolder)
            RETURN COUNT(f) > 0 as hasMapping
            """
            
            is_initial_mapping_params = {
                "org_id": request.organisation_id,
                "general_dept": DefaultDepartments.GENERAL.value
            }
            
            mapping_result = execute_read_query(is_initial_mapping_query, is_initial_mapping_params)
            is_initial_mapping = mapping_result[0]['hasMapping'] if mapping_result else False

            return organisation_pb2.ListSourcesResponse(
                success=True,
                message=f"Found {len(sources)} sources",
                sources=sources,
                isInitialMapping=is_initial_mapping
            )

        except Exception as e:
            logger.error("Error listing sources", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing sources: {str(e)}")
            return organisation_pb2.ListSourcesResponse(success=False, message=f"Error listing sources: {str(e)}")

    def deleteSource(self, request, context):
        """
        Delete a source.

        Args:
            request: Contains source_id and user_id
            context: gRPC context

        Returns:
            Response indicating success/failure
        """
        logger.info("Received request to delete source", source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate user is an admin of the organization that owns the source
            admin_query = self.queries.VALIDATE_SOURCE_DELETE_PERMISSION
            admin_params = {
                "user_id": request.user_id,
                "source_id": request.source_id
            }

            admin_result = True #execute_read_query(admin_query, admin_params)

            if not admin_result:
                logger.error(f"User {request.user_id} is not authorized to delete source {request.source_id}")
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(f"Only organization admins can delete sources")
                return organisation_pb2.DeleteSourceResponse(success=False, message=f"Permission denied")

            # Delete the source using repository
            query = self.queries.DELETE_SOURCE
            params = {
                "source_id": request.source_id
            }

            execute_write_query(query, params)

            return organisation_pb2.DeleteSourceResponse(
                success=True,
                message="Source deleted successfully"
            )

        except Exception as e:
            logger.error("Error deleting source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error deleting source: {str(e)}")
            return organisation_pb2.DeleteSourceResponse(success=False, message=f"Error deleting source: {str(e)}")

    def updateSourceCredentials(self, request, context):
        """
        Update credentials for an existing source.
        """
        logger.info("Received request to update source credentials", 
                   source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate that service account key is provided
            if not request.service_account_key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Service account key must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Service account key must be provided"
                )

            # Get existing source
            source_query = self.queries.GET_SOURCE_BY_ID
            source_params = {"source_id": request.source_id}
            source_result = execute_read_query(source_query, source_params)

            if not source_result:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source not found")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False, 
                    message="Source not found"
                )

            source_data = source_result[0]['s']
            organisation_id = source_result[0]['org_id']

            # Validate JSON format for service account key
            try:
                json.loads(request.service_account_key)
                service_account_key = request.service_account_key
            except json.JSONDecodeError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in service_account_key")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Invalid JSON format in service_account_key"
                )

            # Validate service account if provided
            validation_success = True
            validation_message = "Credentials updated successfully"
            
            if (service_account_key and 
                source_data.get('type') == SourceType.GOOGLE_DRIVE.value):
                
                validation_success, validation_message, accessible_folders = \
                    self.google_sa_manager.validate_service_account_access(service_account_key)
                
                if not validation_success:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Service account validation failed: {validation_message}")
                    return organisation_pb2.UpdateSourceCredentialsResponse(
                        success=False, 
                        message=f"Service account validation failed: {validation_message}"
                    )

            # Update source credentials
            current_time = datetime.utcnow().isoformat()
            update_query = self.queries.UPDATE_SOURCE_CREDENTIALS
            update_params = {
                "source_id": request.source_id,
                "service_account_key": service_account_key,
                "updated_at": current_time
            }

            execute_write_query(update_query, update_params)

            # Update validation status
            validation_query = self.queries.UPDATE_SOURCE_VALIDATION_STATUS
            validation_params = {
                "source_id": request.source_id,
                "is_validated": validation_success,
                "last_validated_at": current_time if validation_success else None,
                "validation_message": validation_message
            }

            execute_write_query(validation_query, validation_params)

            # Create updated source model for response
            source_model = organisation_pb2.SourceModel(
                id=request.source_id,
                organisation_id=organisation_id,
                type=organisation_pb2.SourceType.GOOGLE_DRIVE,  # Assuming Google Drive for now
                name=source_data.get('name', ''),
                created_at=source_data.get('created_at', ''),
                updated_at=current_time
            )

            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=True,
                message=validation_message,
                source=source_model
            )

        except Exception as e:
            logger.error("Error updating source credentials", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating source credentials: {str(e)}")
            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=False, 
                message=f"Error updating source credentials: {str(e)}"
            )
    
    def validateSource(self, request, context):
        """
        Validate a source and return accessible folders.
        """
        logger.info("Received request to validate source", 
                   source_id=request.source_id, org_id=request.organisation_id)

        try:
            # Get source data
            source_query = self.queries.GET_SOURCE_BY_ID
            source_params = {"source_id": request.source_id}
            source_result = execute_read_query(source_query, source_params)

            if not source_result:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source not found")
                return organisation_pb2.ValidateSourceResponse(
                    success=False,
                    message="Source not found",
                    accessible_folders=[]
                )

            source_data = source_result[0]['s']
            service_account_key = source_data.get('service_account_key')

            # Validate service account credentials
            if service_account_key and source_data.get('type') == SourceType.GOOGLE_DRIVE.value:
                validation_success, validation_message, accessible_folders = \
                    self.google_sa_manager.validate_service_account_access(service_account_key)
                
                # Convert folders to proto format
                proto_folders = []
                for folder in accessible_folders:
                    proto_folder = organisation_pb2.Folder(
                        id=folder['id'],
                        name=folder['name']
                    )
                    proto_folders.append(proto_folder)
                
                return organisation_pb2.ValidateSourceResponse(
                    success=validation_success,
                    message=validation_message,
                    accessible_folders=proto_folders
                )
            
            else:
                return organisation_pb2.ValidateSourceResponse(
                    success=False,
                    message="No valid service account credentials found",
                    accessible_folders=[]
                )

        except Exception as e:
            logger.error("Error validating source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error validating source: {str(e)}")
            return organisation_pb2.ValidateSourceResponse(
                success=False,
                message=f"Error validating source: {str(e)}",
                accessible_folders=[]
            )

    def deleteSource(self, request, context):
        """
        Delete a source.

        Args:
            request: Contains source_id and user_id
            context: gRPC context

        Returns:
            Response indicating success/failure
        """
        logger.info("Received request to delete source", source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate user is an admin of the organization that owns the source
            admin_query = self.queries.VALIDATE_SOURCE_DELETE_PERMISSION
            admin_params = {
                "user_id": request.user_id,
                "source_id": request.source_id
            }

            admin_result = True #execute_read_query(admin_query, admin_params)

            if not admin_result:
                logger.error(f"User {request.user_id} is not authorized to delete source {request.source_id}")
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(f"Only organization admins can delete sources")
                return organisation_pb2.DeleteSourceResponse(success=False, message=f"Permission denied")

            # Delete the source using repository
            query = self.queries.DELETE_SOURCE
            params = {
                "source_id": request.source_id
            }

            execute_write_query(query, params)

            return organisation_pb2.DeleteSourceResponse(
                success=True,
                message="Source deleted successfully"
            )

        except Exception as e:
            logger.error("Error deleting source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error deleting source: {str(e)}")
            return organisation_pb2.DeleteSourceResponse(success=False, message=f"Error deleting source: {str(e)}")