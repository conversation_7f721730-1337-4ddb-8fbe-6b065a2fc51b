import json
import tempfile
import os
import structlog
from typing import <PERSON>ple
from app.db.neo4j import execute_read_query
from app.utils.constants.sources import SourceType

logger = structlog.get_logger()

def get_source_credentials(organisation_id, source_type=None, credential_type='oauth'):
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
    """
    Retrieve credentials from a Source node.
    
    Args:
        organisation_id: The ID of the organization
        source_type: The type of source (default: google_drive)
        credential_type: Type of credentials to retrieve ('oauth' or 'service_account')
        
    Returns:
        The credentials JSON string or None if not found
    """
    if credential_type == 'service_account':
        field = 's.service_account_key'
    else:
        field = 's.oauth_credentials'
    
    query = f"""
    MATCH (o:Organisation {{id: $org_id}})-[:HAS_SOURCE]->(s:Source)
    WHERE s.type = $source_type
    RETURN {field}
    """
    
    params = {
        "org_id": organisation_id,
        "source_type": source_type
    }
    
    result = execute_read_query(query, params)
    if not result or not result[0].get(field):
        logger.error(f"No {credential_type} credentials found for organization {organisation_id}")
        return None
    
    return result[0][field]

def get_credentials_as_tempfile(organisation_id, source_type=None, credential_type='oauth'):
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
    """
    Retrieve credentials from a Source node and save to a temporary file.
    
    Args:
        organisation_id: The ID of the organization
        source_type: The type of source (default: google_drive)
        credential_type: Type of credentials to retrieve ('oauth' or 'service_account')
        
    Returns:
        Tuple containing:
        - success: Boolean indicating if operation was successful
        - result: Path to temporary file if successful, error message if not
    """
    credentials_json = get_source_credentials(organisation_id, source_type, credential_type)
    if not credentials_json:
        return False, f"No {credential_type} credentials found for organization {organisation_id}"
    
    try:
        # Create a temporary file with the credentials
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp:
            temp.write(credentials_json)
            temp_path = temp.name
        
        return True, temp_path
    except Exception as e:
        logger.error(f"Error creating temporary credentials file: {str(e)}")
        return False, f"Error creating temporary credentials file: {str(e)}"

def cleanup_tempfile(temp_path):
    """
    Clean up a temporary file.
    
    Args:
        temp_path: Path to the temporary file
    """
    try:
        os.unlink(temp_path)
    except Exception as e:
        logger.error(f"Error cleaning up temporary file: {str(e)}")

def detect_credential_type(credentials_json: str) -> str:
    """
    Detect if credentials are OAuth or Service Account.
    
    Args:
        credentials_json: JSON string of credentials
        
    Returns:
        "oauth" or "service_account"
    """
    try:
        creds = json.loads(credentials_json)
        
        # Service account keys have 'type': 'service_account'
        if creds.get('type') == 'service_account':
            return 'service_account'
        
        # OAuth credentials have 'installed' or 'web' keys
        if 'installed' in creds or 'web' in creds:
            return 'oauth'
        
        # Default to oauth for backward compatibility
        return 'oauth'
        
    except json.JSONDecodeError:
        return 'oauth'  # Default fallback

def get_service_account_credentials(organisation_id: str, source_type: str = None) -> Tuple[bool, str]:
    """
    Get service account credentials for an organisation.
    
    Args:
        organisation_id: Organisation ID
        source_type: Source type (default: google_drive)
        
    Returns:
        Tuple of (success, credentials_json_or_error_message)
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
    
    credentials_json = get_source_credentials(organisation_id, source_type, 'service_account')
    if not credentials_json:
        return False, f"No service account credentials found for organization {organisation_id}"
    
    return True, credentials_json