"""
LLM client for entity extraction and knowledge graph enhancement.
Supports multiple providers: requesty (default), openrouter, openai, anthropic, gemini.
"""

import json
import os
import re
from typing import Dict, Any, Optional, List
import structlog

from app.constants.entities import EntityType, get_all_entity_types, is_valid_entity_type
from app.constants.relationships import RelationshipType, get_all_relationship_types, is_valid_relationship_type
from app.constants.schemas import EntityRelationship, Entity
from .prompt_generator import create_full_prompt_generator, PromptGenerator
from .prompt_templates import create_query_analysis_prompt

logger = structlog.get_logger()


class LLMClient:
    """
    Client for LLM-based entity extraction and query analysis.
    """
    
    def __init__(self, provider: str = "requesty", model: str = "gpt-4o", api_key: Optional[str] = None):
        """
        Initialize the LLM client with support for multiple providers.
        
        Args:
            provider: LLM provider (requesty, openrouter, openai, anthropic, gemini)
            model: Model name to use
            api_key: API key (if None, will use environment variable)
        """
        self.provider = provider.lower()
        self.model = model
        self.api_key = api_key or self._get_api_key()
        
        # Initialize prompt generator
        self.prompt_generator = create_full_prompt_generator()
        
        # Initialize the appropriate client
        self.client = self._initialize_client()
        
        if self.client:
            logger.info(f"LLM client initialized with provider: {self.provider}, model: {model}")
        else:
            logger.warning(f"LLM client initialization failed for provider: {self.provider}. Features will be disabled.")
    
    def _get_api_key(self) -> Optional[str]:
        """Get API key from environment variables based on provider."""
        env_vars = {
            "openai": "OPENAI_API_KEY",
            "anthropic": "ANTHROPIC_API_KEY",
            "gemini": "GEMINI_API_KEY",
            "openrouter": "OPENROUTER_API_KEY",
            "requesty": "REQUESTY_API_KEY"
        }
        return os.getenv(env_vars.get(self.provider), "sk-px2KBcd3Rk+b61DEXrEY37hcWAtfBcl0gGzxxGiAO2kbP69gMtgKT7eecg7oGKC26FCx2J0s1L4NWa5QzdoWKX2UOSETwFesPoWiBGbXdAY=")

    def _initialize_client(self):
        """Initialize the appropriate LLM client based on provider."""
        if not self.api_key:
            logger.warning(f"No API key provided for {self.provider}. LLM features will be disabled.")
            return None

        try:
            if self.provider == "openai":
                import openai
                return openai.OpenAI(api_key=self.api_key)
            elif self.provider == "openrouter":
                import openai
                return openai.OpenAI(
                    api_key=self.api_key,
                    base_url="https://openrouter.ai/api/v1"
                )
            elif self.provider == "requesty":
                import openai
                base_url = os.getenv("REQUESTY_BASE_URL", "https://router.requesty.ai/v1")
                return openai.OpenAI(
                    api_key=self.api_key,
                    base_url=base_url
                )
            elif self.provider == "gemini":
                # Note: Gemini would need google-generativeai package
                logger.warning("Gemini provider not fully implemented yet")
                return None
            else:
                logger.warning(f"Provider {self.provider} not supported")
                return None
        except ImportError as e:
            logger.error(f"Required package not installed for {self.provider}: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to initialize {self.provider} client: {str(e)}")
            return None

    def is_available(self) -> bool:
        """Check if the LLM client is available."""
        return self.client is not None
    
    def extract_entities_and_relationships(self, text: str) -> Dict[str, Any]:
        """
        Extract entities and relationships from text using LLM with multi-provider support.
        
        Args:
            text: The text to analyze
            
        Returns:
            Dictionary containing extracted entities and relationships as schema objects
        """
        if not self.is_available():
            logger.warning("LLM client not available, using fallback heuristics")
            return self._fallback_extraction(text)
        
        if not text or len(text.strip()) < 10:
            logger.debug("Text too short for entity extraction")
            return {"entities": [], "relationships": []}
        
        try:
            # Generate dynamic prompt using the new prompt generator
            prompt = self.prompt_generator.generate_relationship_extraction_prompt(text)
            schema_description = self.prompt_generator.get_schema_description()
            
            # Use the new structured response method that supports multiple providers
            relationships = self.generate_structured_response(prompt, schema_description)
            
            # Handle case where response is a dict instead of list
            if isinstance(relationships, dict) and not relationships:
                logger.warning("Empty response from LLM, using fallback")
                return self._fallback_extraction(text)
            
            # Ensure we have a list of relationships
            if isinstance(relationships, dict):
                relationships = [relationships]
            elif not isinstance(relationships, list):
                logger.warning("Unexpected response format, using fallback")
                return self._fallback_extraction(text)
            
            # Convert to schema objects and extract entities
            validated_result = self._convert_to_schema_objects(relationships)
            
            logger.info(f"Extracted {len(validated_result['entities'])} entities and {len(validated_result['relationships'])} relationships")
            return validated_result
            
        except Exception as e:
            logger.error(f"Error extracting entities and relationships: {str(e)}")
            # Fallback to heuristic extraction
            return self._fallback_extraction(text)
    
    def generate_structured_response(self, prompt: str, schema_description: str) -> Dict[str, Any]:
        """
        Generate a structured response from the LLM using multi-provider support.

        Args:
            prompt: The input prompt
            schema_description: Description of the expected output schema

        Returns:
            Parsed JSON response
        """
        if not self.client:
            raise ValueError("LLM client not initialized")

        system_prompt = f"""
You are an expert at extracting structured information from enterprise documents.
You must respond with ONLY valid JSON that matches this schema - no explanatory text before or after:

{schema_description}

IMPORTANT: Return ONLY the JSON array/object. Do not include any explanatory text, comments, or formatting outside the JSON.
Be precise and only extract information that is clearly stated or strongly implied in the text.
"""

        try:
            if self.provider in ["openai", "openrouter", "requesty"]:
                response = self.client.chat.completions.create(
                    model="openai/gpt-4o",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1,
                    max_tokens=3000
                )
                content = response.choices[0].message.content
            elif self.provider == "anthropic":
                response = self.client.messages.create(
                    model=self.model,
                    max_tokens=4000,
                    system=system_prompt,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1
                )
                content = response.content[0].text
            else:
                raise ValueError(f"Provider {self.provider} not supported for structured response")

            return self._extract_json_from_response(content)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {content}")
            return {}
        except Exception as e:
            logger.error(f"LLM API error: {e}")
            return {}

    def _extract_json_from_response(self, content: str) -> Dict[str, Any]:
        """Extract JSON from LLM response, handling cases where there might be extra text."""
        # First try to parse the content directly
        try:
            return json.loads(content.strip())
        except json.JSONDecodeError:
            pass

        # Clean up common markdown formatting
        if content.startswith('```json'):
            content = content.replace('```json', '').replace('```', '').strip()
        elif content.startswith('```'):
            content = content.replace('```', '').strip()

        # Try again after cleanup
        try:
            return json.loads(content.strip())
        except json.JSONDecodeError:
            pass

        # Try to find JSON array or object in the response using regex
        json_patterns = [
            r'\[.*\]',  # JSON array
            r'\{.*\}',  # JSON object
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

        # If no valid JSON found, return empty structure
        logger.warning("No valid JSON found in LLM response")
        logger.debug(f"Raw content: {content}")
        return {}

    def _parse_structured_response(self, content: str) -> List[Dict[str, Any]]:
        """
        Legacy method for backward compatibility.
        
        Args:
            content: Raw response content from LLM
            
        Returns:
            List of relationship dictionaries
        """
        result = self._extract_json_from_response(content)
        
        if isinstance(result, dict):
            return [result]
        elif isinstance(result, list):
            return result
        else:
            return []
    
    def _convert_to_schema_objects(self, relationships_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Convert raw relationship data to schema objects.
        
        Args:
            relationships_data: List of relationship dictionaries
            
        Returns:
            Dictionary with entities and relationships as schema objects
        """
        entities = {}
        relationships = []
        
        for rel_data in relationships_data:
            try:
                # Create EntityRelationship object
                relationship = EntityRelationship(
                    subject=rel_data.get('subject', '').strip(),
                    predicate=rel_data.get('predicate', '').strip(),
                    object=rel_data.get('object', '').strip(),
                    subject_type=rel_data.get('subject_type', '').strip(),
                    object_type=rel_data.get('object_type', '').strip(),
                    confidence_score=rel_data.get('confidence_score', 0.8),
                    context=rel_data.get('context', '').strip(),
                    source_sentence=rel_data.get('source_sentence', '').strip()
                )
                
                # Validate using constants
                if not is_valid_relationship_type(relationship.predicate):
                    logger.warning(f"Invalid relationship type: {relationship.predicate}")
                    continue
                
                relationships.append(relationship)
                
                # Extract entities from relationship
                if relationship.subject and relationship.subject_type:
                    if relationship.subject not in entities:
                        entities[relationship.subject] = Entity(
                            name=relationship.subject,
                            entity_type=relationship.subject_type,
                            confidence_score=relationship.confidence_score
                        )
                
                if relationship.object and relationship.object_type:
                    if relationship.object not in entities:
                        entities[relationship.object] = Entity(
                            name=relationship.object,
                            entity_type=relationship.object_type,
                            confidence_score=relationship.confidence_score
                        )
                
            except Exception as e:
                logger.error(f"Error creating relationship object: {e}")
                continue
        
        return {
            "entities": list(entities.values()),
            "relationships": relationships
        }
    
    def _fallback_extraction(self, text: str) -> Dict[str, Any]:
        """
        Fallback heuristic extraction when LLM is not available.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with basic entities and relationships
        """
        entities = []
        relationships = []
        
        # Simple heuristic extraction
        words = text.split()
        
        # Look for potential person names (capitalized words)
        for i, word in enumerate(words):
            if word.istitle() and len(word) > 2:
                # Check if next word is also capitalized (potential full name)
                if i + 1 < len(words) and words[i + 1].istitle():
                    full_name = f"{word} {words[i + 1]}"
                    entities.append(Entity(
                        name=full_name,
                        entity_type="Person",
                        confidence_score=0.6
                    ))
        
        # Look for potential companies (words ending with Corp, Inc, LLC, etc.)
        company_suffixes = ['Corp', 'Inc', 'LLC', 'Ltd', 'Company', 'Technologies', 'Systems']
        for i, word in enumerate(words):
            if any(word.endswith(suffix) for suffix in company_suffixes):
                # Include previous word if capitalized
                if i > 0 and words[i-1].istitle():
                    company_name = f"{words[i-1]} {word}"
                else:
                    company_name = word
                entities.append(Entity(
                    name=company_name,
                    entity_type="Company",
                    confidence_score=0.5
                ))
        
        logger.info(f"Fallback extraction found {len(entities)} entities")
        return {
            "entities": entities,
            "relationships": relationships
        }

    def analyze_query(self, query: str) -> Dict[str, Any]:
        """
        Analyze a search query to determine routing strategy.
        
        Args:
            query: The search query to analyze
            
        Returns:
            Dictionary containing query analysis results
        """
        if not self.is_available():
            # Fallback to simple heuristics
            return self._fallback_query_analysis(query)
        
        try:
            # Create analysis prompt
            prompt = create_query_analysis_prompt(query)
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="openai/gpt-4o",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=500
            )
            
            # Parse response
            content = response.choices[0].message.content.strip()
            result = self._parse_query_analysis_response(content)
            
            logger.debug(f"Query analysis: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing query: {str(e)}")
            return self._fallback_query_analysis(query)
    
    def _parse_extraction_response(self, content: str) -> Dict[str, Any]:
        """Parse the LLM response for entity extraction."""
        try:
            # Try to extract JSON from the response
            if "```json" in content:
                start = content.find("```json") + 7
                end = content.find("```", start)
                json_str = content[start:end].strip()
            elif "{" in content and "}" in content:
                start = content.find("{")
                end = content.rfind("}") + 1
                json_str = content[start:end]
            else:
                json_str = content
            
            result = json.loads(json_str)
            
            # Ensure required keys exist
            if "entities" not in result:
                result["entities"] = []
            if "relationships" not in result:
                result["relationships"] = []
                
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {str(e)}")
            logger.debug(f"Response content: {content}")
            return {"entities": [], "relationships": []}
    
    def _parse_query_analysis_response(self, content: str) -> Dict[str, Any]:
        """Parse the LLM response for query analysis."""
        try:
            # Try to extract JSON from the response
            if "```json" in content:
                start = content.find("```json") + 7
                end = content.find("```", start)
                json_str = content[start:end].strip()
            elif "{" in content and "}" in content:
                start = content.find("{")
                end = content.rfind("}") + 1
                json_str = content[start:end]
            else:
                json_str = content
            
            result = json.loads(json_str)
            
            # Set defaults for missing keys
            result.setdefault("query_type", "simple")
            result.setdefault("needs_graph_search", False)
            result.setdefault("entity_focus", False)
            result.setdefault("relationship_focus", False)
            result.setdefault("reasoning", "")
            
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse query analysis JSON: {str(e)}")
            return self._fallback_query_analysis("")
    
    def _validate_extraction_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean extraction results."""
        validated_entities = []
        validated_relationships = []
        
        # Validate entities
        for entity in result.get("entities", []):
            if not isinstance(entity, dict):
                continue
                
            name = entity.get("name", "").strip()
            entity_type = entity.get("type", "").strip()
            description = entity.get("description", "").strip()
            
            if not name or not entity_type:
                continue
            
            # Validate entity type
            if not is_valid_entity_type(entity_type):
                # Try to find a close match or default to ENTITY
                entity_type = "Entity"
            
            validated_entities.append({
                "name": name,
                "type": entity_type,
                "description": description
            })
        
        # Validate relationships
        entity_names = {e["name"] for e in validated_entities}
        
        for relationship in result.get("relationships", []):
            if not isinstance(relationship, dict):
                continue
                
            source = relationship.get("source", "").strip()
            target = relationship.get("target", "").strip()
            rel_type = relationship.get("type", "").strip()
            description = relationship.get("description", "").strip()
            
            if not source or not target or not rel_type:
                continue
            
            # Only include relationships where both entities were extracted
            if source not in entity_names or target not in entity_names:
                continue
            
            # Validate relationship type
            if not is_valid_relationship_type(rel_type):
                # Default to RELATED_TO for invalid types
                rel_type = "RELATED_TO"
            
            validated_relationships.append({
                "source": source,
                "target": target,
                "type": rel_type,
                "description": description
            })
        
        return {
            "entities": validated_entities,
            "relationships": validated_relationships
        }
    
    def _fallback_query_analysis(self, query: str) -> Dict[str, Any]:
        """Fallback query analysis using simple heuristics."""
        query_lower = query.lower()
        
        # Check for relationship keywords
        relationship_keywords = [
            "who reports to", "reports to", "manages", "works for", "leads",
            "team members", "department", "responsible for", "owns",
            "collaborates with", "partners with"
        ]
        
        # Check for entity keywords
        entity_keywords = [
            "person", "employee", "manager", "team", "department", 
            "project", "system", "application", "document"
        ]
        
        has_relationship_focus = any(keyword in query_lower for keyword in relationship_keywords)
        has_entity_focus = any(keyword in query_lower for keyword in entity_keywords)
        
        needs_graph_search = has_relationship_focus or (has_entity_focus and len(query.split()) > 5)
        
        query_type = "relational" if has_relationship_focus else ("entity_focused" if has_entity_focus else "simple")
        
        return {
            "query_type": query_type,
            "needs_graph_search": needs_graph_search,
            "entity_focus": has_entity_focus,
            "relationship_focus": has_relationship_focus,
            "reasoning": "Fallback heuristic analysis"
        }