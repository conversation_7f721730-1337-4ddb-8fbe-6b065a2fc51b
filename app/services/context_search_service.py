"""
Context Search Service

This service provides context retrieval and search functionality that is independent
of any specific connector. It acts as a unified interface for searching across
all connected data sources and knowledge bases.
"""

from typing import List, Dict, Any, Optional, Tuple
import time
import structlog
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.search.hybrid_search_engine import HybridSearchEngine
from app.interfaces.search_interface import (
    IContextSearchProvider, SearchRequest, SearchResponse, SearchResult,
    BatchSearchRequest, BatchSearchResponse, SearchType
)

logger = structlog.get_logger()


class ContextSearchService:
    """
    Independent context search service for knowledge base retrieval.
    
    This service provides search functionality that works across all connectors
    and data sources, making it truly connector-agnostic.
    """
    
    def __init__(self):
        """Initialize the context search service."""
        self.pinecone_service = PineconeService()
    
    def is_initialized(self) -> bool:
        """
        Check if the service is properly initialized.
        
        Returns:
            <PERSON><PERSON>an indicating if the service is initialized
        """
        return self.pinecone_service.is_initialized()
    
    def search_similar_documents(self,
                                user_id: str,
                                query_text: str,
                                top_k: int = 5,
                                agent_id: Optional[str] = None,
                                organisation_id: str = None,
                                file_ids: Optional[List[str]] = None,
                                connector_types: Optional[List[str]] = None) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        Search for documents semantically similar to the query text across all connectors.

        Args:
            user_id: The ID of the user
            query_text: The query text to search for
            top_k: The number of results to return
            agent_id: Optional ID of the agent. If provided, only return files accessible to both user and agent's department
            organisation_id: ID of the organization (mandatory but not used currently)
            file_ids: Optional list of specific file IDs to search within. If provided, search is limited to these files only.
            connector_types: Optional list of connector types to search within (e.g., ['google_drive', 'slack'])

        Returns:
            Tuple containing:
            - success: Boolean indicating if search was successful
            - message: Status message
            - results: List of search results
        """
        if not self.is_initialized():
            return False, "Context search service not initialized", []

        try:
            # Initialize hybrid search engine with pinecone service
            hybrid_engine = HybridSearchEngine(pinecone_service=self.pinecone_service)
            
            # Get accessible file IDs (this method handles access control)
            search_file_ids = self.pinecone_service.get_search_file_ids(user_id, agent_id, file_ids)
            
            # If specific file IDs are provided, intersect with accessible files
            if file_ids:
                search_file_ids = file_ids & search_file_ids
            
            # Filter by connector types if specified
            if connector_types:
                search_file_ids = self._filter_by_connector_types(search_file_ids, connector_types, organisation_id)
            
            # Perform hybrid search
            hybrid_response = hybrid_engine.search_with_query(
                query_text=query_text,
                user_id=user_id,
                top_k=top_k,
                agent_id=agent_id,
                organisation_id=organisation_id,
                file_ids=search_file_ids
            )
            
            if not hybrid_response.success:
                return False, hybrid_response.message, []
            
            # Format results for consistent output
            results = []
            for result in hybrid_response.results:
                results.append({
                    'file_id': result.get('file_id'),
                    'file_name': result.get('file_name'),
                    'mime_type': result.get('mime_type', ''),
                    'web_view_link': result.get('web_view_link', ''),
                    'created_time': result.get('created_time'),
                    'modified_time': result.get('modified_time'),
                    'score': result.get('score'),
                    'vector_id': result.get('vector_id'),
                    'chunk_text': result.get('chunk_text', ''),
                    'connector_type': result.get('connector_type', 'unknown'),
                    'search_type': result.get('search_type', 'hybrid'),
                    'entities': result.get('entities', []),
                    'relationships': result.get('relationships', [])
                })
            
            return hybrid_response.success, hybrid_response.message, results
            
        except Exception as e:
            logger.error(f"Error in context search: {str(e)}")
            return False, f"Error in context search: {str(e)}", []

    def batch_search_similar_documents(self,
                                     user_id: str,
                                     query_texts: List[str],
                                     top_k: int = 5,
                                     agent_id: Optional[str] = None,
                                     organisation_id: str = None,
                                     file_ids: Optional[List[str]] = None,
                                     connector_types: Optional[List[str]] = None) -> Tuple[bool, str, List[List[Dict[str, Any]]]]:
        """
        Perform batch search for documents semantically similar to multiple query texts.
        This is more efficient than calling search_similar_documents multiple times.

        Args:
            user_id: The ID of the user
            query_texts: List of query texts to search for
            top_k: The number of results to return for each query
            agent_id: Optional ID of the agent. If provided, only return files accessible to both user and agent's department
            organisation_id: ID of the organization (mandatory but not used currently)
            file_ids: Optional list of specific file IDs to search within. If provided, search is limited to these files only.
            connector_types: Optional list of connector types to search within

        Returns:
            Tuple containing:
            - success: Boolean indicating if search was successful
            - message: Status message
            - results: List of search results for each query, where each item is a list of results
        """
        if not self.is_initialized():
            return False, "Context search service not initialized", []

        try:
            # Get accessible file IDs
            search_file_ids = self.pinecone_service.get_search_file_ids(user_id, agent_id, file_ids)
            
            # If specific file IDs are provided, intersect with accessible files
            if file_ids:
                search_file_ids = file_ids & search_file_ids
            
            # Filter by connector types if specified
            if connector_types:
                search_file_ids = self._filter_by_connector_types(search_file_ids, connector_types, organisation_id)
            
            # Use the pinecone service's batch search functionality
            return self.pinecone_service.batch_search_similar_documents(
                user_id, query_texts, top_k, agent_id, organisation_id, list(search_file_ids))
                
        except Exception as e:
            logger.error(f"Error in batch context search: {str(e)}")
            return False, f"Error in batch context search: {str(e)}", []

    def _filter_by_connector_types(self, file_ids: set, connector_types: List[str], organisation_id: str) -> set:
        """
        Filter file IDs by connector types.
        
        Args:
            file_ids: Set of file IDs to filter
            connector_types: List of connector types to include
            organisation_id: Organization ID for context
            
        Returns:
            Filtered set of file IDs
        """
        # This is a placeholder implementation
        # In a real implementation, you would query the database to get file metadata
        # and filter based on the connector type that created each file
        
        # For now, return all file IDs as we don't have connector type metadata
        # This should be implemented when file metadata includes connector_type
        logger.info(f"Filtering {len(file_ids)} files by connector types: {connector_types}")
        return file_ids

    def get_search_statistics(self, user_id: str, organisation_id: str) -> Dict[str, Any]:
        """
        Get search statistics for a user/organization.
        
        Args:
            user_id: The ID of the user
            organisation_id: ID of the organization
            
        Returns:
            Dictionary containing search statistics
        """
        try:
            # Get accessible file count
            search_file_ids = self.pinecone_service.get_search_file_ids(user_id, None, None)
            
            return {
                'total_searchable_files': len(search_file_ids),
                'service_initialized': self.is_initialized(),
                'available_connectors': self._get_available_connectors(organisation_id)
            }
            
        except Exception as e:
            logger.error(f"Error getting search statistics: {str(e)}")
            return {
                'total_searchable_files': 0,
                'service_initialized': False,
                'available_connectors': [],
                'error': str(e)
            }

    def _get_available_connectors(self, organisation_id: str) -> List[str]:
        """
        Get list of available connectors for an organization.
        
        Args:
            organisation_id: ID of the organization
            
        Returns:
            List of available connector types
        """
        # This is a placeholder implementation
        # In a real implementation, you would query the database to get
        # the list of connectors configured for the organization
        return ['google_drive']  # Default for now
