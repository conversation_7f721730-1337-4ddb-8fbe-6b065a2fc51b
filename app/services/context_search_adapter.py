"""
Context Search Adapter

This module provides an adapter that implements the IContextSearchProvider interface
using the ContextSearchService, making it compatible with the standardized search interface.
"""

import time
from typing import List
import structlog
from app.interfaces.search_interface import (
    IContextSearchProvider, SearchRequest, SearchResponse, SearchResult,
    BatchSearchRequest, BatchSearchResponse, SearchType
)
from .context_search_service import ContextSearchService

logger = structlog.get_logger()


class ContextSearchAdapter(IContextSearchProvider):
    """
    Adapter that makes ContextSearchService compatible with IContextSearchProvider interface.
    """
    
    def __init__(self):
        """Initialize the adapter with the context search service."""
        self.context_search_service = ContextSearchService()
    
    def search(self, request: SearchRequest) -> SearchResponse:
        """
        Perform a context search based on the provided request.
        
        Args:
            request: The search request containing all search parameters
            
        Returns:
            SearchResponse containing the search results
        """
        start_time = time.time()
        
        try:
            # Call the underlying service
            success, message, results = self.context_search_service.search_similar_documents(
                user_id=request.user_id,
                query_text=request.query_text,
                top_k=request.top_k,
                agent_id=request.agent_id,
                organisation_id=request.organisation_id,
                file_ids=request.file_ids,
                connector_types=request.connector_types
            )
            
            # Convert results to standardized format
            search_results = []
            for result in results:
                search_result = SearchResult(
                    file_id=result['file_id'],
                    file_name=result['file_name'],
                    score=float(result['score']),
                    vector_id=result['vector_id'],
                    chunk_text=result['chunk_text'],
                    connector_type=result['connector_type'],
                    mime_type=result.get('mime_type'),
                    web_view_link=result.get('web_view_link'),
                    created_time=result.get('created_time'),
                    modified_time=result.get('modified_time'),
                    search_type=result.get('search_type'),
                    entities=result.get('entities'),
                    relationships=result.get('relationships')
                )
                search_results.append(search_result)
            
            search_time_ms = (time.time() - start_time) * 1000
            
            return SearchResponse(
                success=success,
                message=message,
                results=search_results,
                total_results=len(search_results),
                search_time_ms=search_time_ms
            )
            
        except Exception as e:
            search_time_ms = (time.time() - start_time) * 1000
            logger.error(f"Error in search adapter: {str(e)}")
            return SearchResponse(
                success=False,
                message=f"Search error: {str(e)}",
                results=[],
                total_results=0,
                search_time_ms=search_time_ms
            )
    
    def batch_search(self, request: BatchSearchRequest) -> BatchSearchResponse:
        """
        Perform batch context search for multiple queries.
        
        Args:
            request: The batch search request containing multiple queries
            
        Returns:
            BatchSearchResponse containing results for all queries
        """
        start_time = time.time()
        
        try:
            # Call the underlying service
            success, message, batch_results = self.context_search_service.batch_search_similar_documents(
                user_id=request.user_id,
                query_texts=request.query_texts,
                top_k=request.top_k,
                agent_id=request.agent_id,
                organisation_id=request.organisation_id,
                file_ids=request.file_ids,
                connector_types=request.connector_types
            )
            
            # Convert results to standardized format
            all_search_results = []
            total_results = 0
            
            for query_results in batch_results:
                search_results = []
                for result in query_results:
                    search_result = SearchResult(
                        file_id=result['file_id'],
                        file_name=result['file_name'],
                        score=float(result['score']),
                        vector_id=result['vector_id'],
                        chunk_text=result['chunk_text'],
                        connector_type=result.get('connector_type', 'unknown'),
                        mime_type=result.get('mime_type'),
                        web_view_link=result.get('web_view_link'),
                        created_time=result.get('created_time'),
                        modified_time=result.get('modified_time'),
                        search_type=result.get('search_type'),
                        entities=result.get('entities'),
                        relationships=result.get('relationships')
                    )
                    search_results.append(search_result)
                
                all_search_results.append(search_results)
                total_results += len(search_results)
            
            search_time_ms = (time.time() - start_time) * 1000
            
            return BatchSearchResponse(
                success=success,
                message=message,
                results=all_search_results,
                total_queries=len(request.query_texts),
                total_results=total_results,
                search_time_ms=search_time_ms
            )
            
        except Exception as e:
            search_time_ms = (time.time() - start_time) * 1000
            logger.error(f"Error in batch search adapter: {str(e)}")
            return BatchSearchResponse(
                success=False,
                message=f"Batch search error: {str(e)}",
                results=[],
                total_queries=len(request.query_texts),
                total_results=0,
                search_time_ms=search_time_ms
            )
    
    def is_available(self) -> bool:
        """
        Check if the search provider is available and ready to serve requests.
        
        Returns:
            Boolean indicating if the provider is available
        """
        return self.context_search_service.is_initialized()
    
    def get_supported_search_types(self) -> List[SearchType]:
        """
        Get the list of search types supported by this provider.
        
        Returns:
            List of supported search types
        """
        return [SearchType.SEMANTIC, SearchType.HYBRID, SearchType.GRAPH]
    
    def get_supported_connector_types(self) -> List[str]:
        """
        Get the list of connector types supported by this provider.
        
        Returns:
            List of supported connector types
        """
        # This should be dynamic based on registered connectors
        # For now, return the known types
        return ['google_drive', 'slack', 'gmail', 'notion']
