"""
Entity type definitions for Enterprise KG

This module defines all supported entity types in the enterprise knowledge graph.
Add new entity types here as the system evolves.
"""

from enum import Enum
from typing import Set, Dict, Any


class EntityType(Enum):
    """
    Enumeration of all supported entity types in the enterprise knowledge graph.

    Each entity type represents a distinct category of entities that can be
    extracted from enterprise documents, with increased granularity.
    """

    # People, Roles, and Skills
    PERSON = "Person"
    EMPLOYEE = "Employee"
    MANAGER = "Manager"
    EXECUTIVE = "Executive"
    CONSULTANT = "Consultant"
    CLIENT = "Client"
    STAKEHOLDER = "Stakeholder"
    JOB_ROLE = "JobRole"
    SKILL = "Skill"

    # Organizational Units
    COMPANY = "Company"
    DEPARTMENT = "Department"
    TEAM = "Team"
    VENDOR = "Vendor"
    PARTNER = "Partner"
    COMPETITOR = "Competitor"
    BUSINESS_UNIT = "Business Unit"

    # Projects, Products, and Initiatives
    PROJECT = "Project"
    INITIATIVE = "Initiative"
    PROGRAM = "Program"
    CAMPAIGN = "Campaign"
    PRODUCT = "Product"
    SERVICE = "Service"
    FEATURE = "Feature"

    # Documents and Knowledge Assets
    DOCUMENT = "Document"
    REPORT = "Report"
    PROPOSAL = "Proposal"
    CONTRACT = "Contract"
    POLICY = "Policy"
    PROCEDURE = "Procedure"
    KNOWLEDGE_BASE_ARTICLE = "Knowledge Base Article"
    PRESENTATION = "Presentation"

    # Technology and Systems
    SYSTEM = "System"
    APPLICATION = "Application"
    DATABASE = "Database"
    PLATFORM = "Platform"
    TOOL = "Tool"
    TECHNOLOGY = "Technology"
    API = "API"
    SERVER = "Server"
    CLOUD_SERVICE = "Cloud Service"
    SOFTWARE_LIBRARY = "Software Library"
    DATA_SOURCE = "Data Source"

    # Business Concepts
    PROCESS = "Process"
    WORKFLOW = "Workflow"
    REQUIREMENT = "Requirement"
    OBJECTIVE = "Objective"
    GOAL = "Goal"
    METRIC = "Metric"
    KPI = "KPI"
    RISK = "Risk"
    OPPORTUNITY = "Opportunity"
    CAPABILITY = "Capability"

    # Financial
    BUDGET = "Budget"
    COST = "Cost"
    REVENUE = "Revenue"
    INVESTMENT = "Investment"
    FINANCIAL_ACCOUNT = "FinancialAccount"
    INVOICE = "Invoice"
    TRANSACTION = "Transaction"

    # Locations
    OFFICE = "Office"
    LOCATION = "Location"
    REGION = "Region"
    COUNTRY = "Country"
    DATA_CENTER = "Data Center"

    # Time-based & Events
    MILESTONE = "Milestone"
    DEADLINE = "Deadline"
    PHASE = "Phase"
    QUARTER = "Quarter"
    YEAR = "Year"
    EVENT = "Event"
    MEETING = "Meeting"

    # Generic
    ENTITY = "Entity"  # Fallback for unclassified entities


# Helper functions for entity type management
def get_all_entity_types() -> Set[str]:
    """Get all entity type values as strings."""
    return {entity_type.value for entity_type in EntityType}


def get_person_related_types() -> Set[str]:
    """Get entity types related to people, roles, and skills."""
    return {
        EntityType.PERSON.value,
        EntityType.EMPLOYEE.value,
        EntityType.MANAGER.value,
        EntityType.EXECUTIVE.value,
        EntityType.CONSULTANT.value,
        EntityType.CLIENT.value,
        EntityType.STAKEHOLDER.value,
        EntityType.JOB_ROLE.value,
        EntityType.SKILL.value,
    }


def get_project_related_types() -> Set[str]:
    """Get entity types related to projects, products, and initiatives."""
    return {
        EntityType.PROJECT.value,
        EntityType.INITIATIVE.value,
        EntityType.PROGRAM.value,
        EntityType.CAMPAIGN.value,
        EntityType.PRODUCT.value,
        EntityType.SERVICE.value,
        EntityType.FEATURE.value,
    }


def get_organization_related_types() -> Set[str]:
    """Get entity types related to organizations."""
    return {
        EntityType.COMPANY.value,
        EntityType.DEPARTMENT.value,
        EntityType.TEAM.value,
        EntityType.VENDOR.value,
        EntityType.PARTNER.value,
        EntityType.COMPETITOR.value,
        EntityType.BUSINESS_UNIT.value,
    }


def get_system_related_types() -> Set[str]:
    """Get entity types related to systems and technology."""
    return {
        EntityType.SYSTEM.value,
        EntityType.APPLICATION.value,
        EntityType.DATABASE.value,
        EntityType.PLATFORM.value,
        EntityType.TOOL.value,
        EntityType.TECHNOLOGY.value,
        EntityType.API.value,
        EntityType.SERVER.value,
        EntityType.CLOUD_SERVICE.value,
        EntityType.SOFTWARE_LIBRARY.value,
        EntityType.DATA_SOURCE.value,
    }


def get_document_related_types() -> Set[str]:
    """Get entity types related to documents and knowledge assets."""
    return {
        EntityType.DOCUMENT.value,
        EntityType.REPORT.value,
        EntityType.PROPOSAL.value,
        EntityType.CONTRACT.value,
        EntityType.POLICY.value,
        EntityType.PROCEDURE.value,
        EntityType.KNOWLEDGE_BASE_ARTICLE.value,
        EntityType.PRESENTATION.value,
    }


def get_financial_related_types() -> Set[str]:
    """Get entity types related to finance."""
    return {
        EntityType.BUDGET.value,
        EntityType.COST.value,
        EntityType.REVENUE.value,
        EntityType.INVESTMENT.value,
        EntityType.FINANCIAL_ACCOUNT.value,
        EntityType.INVOICE.value,
        EntityType.TRANSACTION.value,
    }


def get_location_related_types() -> Set[str]:
    """Get entity types related to locations."""
    return {
        EntityType.OFFICE.value,
        EntityType.LOCATION.value,
        EntityType.REGION.value,
        EntityType.COUNTRY.value,
        EntityType.DATA_CENTER.value,
    }


def is_valid_entity_type(entity_type: str) -> bool:
    """Check if a string is a valid entity type."""
    return entity_type in get_all_entity_types()


def get_entity_type_description(entity_type: EntityType) -> str:
    """Get a human-readable description for an entity type."""
    # This dictionary is now significantly expanded
    descriptions = {
        EntityType.PERSON: "An individual person mentioned in the document",
        EntityType.EMPLOYEE: "A company employee",
        EntityType.MANAGER: "A person in a management role",
        EntityType.EXECUTIVE: "A senior executive or C-level person",
        EntityType.CONSULTANT: "An external consultant or advisor",
        EntityType.CLIENT: "A customer or client of the organization",
        EntityType.STAKEHOLDER: "A person or group with interest in a project/organization",
        EntityType.JOB_ROLE: "A formal job title or role within the organization",
        EntityType.SKILL: "A specific skill or expertise",
        EntityType.COMPANY: "A company or corporation",
        EntityType.DEPARTMENT: "An organizational department",
        EntityType.TEAM: "A working team or group",
        EntityType.VENDOR: "An external vendor or supplier",
        EntityType.PARTNER: "A business partner",
        EntityType.COMPETITOR: "A competitor organization",
        EntityType.BUSINESS_UNIT: "A distinct business unit within a company",
        EntityType.PROJECT: "A specific project or work initiative",
        EntityType.INITIATIVE: "A strategic initiative or program",
        EntityType.PROGRAM: "A large-scale program containing multiple projects",
        EntityType.CAMPAIGN: "A marketing or business campaign",
        EntityType.PRODUCT: "A commercial product offered by the company",
        EntityType.SERVICE: "A service offered by the company",
        EntityType.FEATURE: "A specific feature of a product or service",
        EntityType.DOCUMENT: "A document or file",
        EntityType.REPORT: "A formal report",
        EntityType.PROPOSAL: "A business proposal",
        EntityType.CONTRACT: "A legal contract or agreement",
        EntityType.POLICY: "An organizational policy",
        EntityType.PROCEDURE: "A standard operating procedure",
        EntityType.KNOWLEDGE_BASE_ARTICLE: "An article in a knowledge base",
        EntityType.PRESENTATION: "A slide deck or presentation",
        EntityType.SYSTEM: "A computer or business system",
        EntityType.APPLICATION: "A software application",
        EntityType.DATABASE: "A database system",
        EntityType.PLATFORM: "A technology platform",
        EntityType.TOOL: "A software tool or utility",
        EntityType.TECHNOLOGY: "A technology or technical solution",
        EntityType.API: "An Application Programming Interface",
        EntityType.SERVER: "A physical or virtual server",
        EntityType.CLOUD_SERVICE: "A cloud computing service (e.g., AWS S3, Azure VM)",
        EntityType.SOFTWARE_LIBRARY: "A software library or framework",
        EntityType.DATA_SOURCE: "A source of data, like a database or API endpoint",
        EntityType.PROCESS: "A business process",
        EntityType.WORKFLOW: "A defined workflow",
        EntityType.REQUIREMENT: "A business or technical requirement",
        EntityType.OBJECTIVE: "A business objective",
        EntityType.GOAL: "A specific goal or target",
        EntityType.METRIC: "A measurement or metric",
        EntityType.KPI: "A key performance indicator",
        EntityType.RISK: "A potential risk or issue",
        EntityType.OPPORTUNITY: "A business opportunity",
        EntityType.CAPABILITY: "A business capability",
        EntityType.BUDGET: "A financial budget",
        EntityType.COST: "A cost or expense",
        EntityType.REVENUE: "Revenue or income",
        EntityType.INVESTMENT: "An investment or funding",
        EntityType.FINANCIAL_ACCOUNT: "A financial account",
        EntityType.INVOICE: "A bill or invoice",
        EntityType.TRANSACTION: "A financial transaction",
        EntityType.OFFICE: "A physical office location",
        EntityType.LOCATION: "A geographic location",
        EntityType.REGION: "A geographic region",
        EntityType.COUNTRY: "A country",
        EntityType.DATA_CENTER: "A data center facility",
        EntityType.MILESTONE: "A project milestone",
        EntityType.DEADLINE: "A deadline or due date",
        EntityType.PHASE: "A project phase",
        EntityType.QUARTER: "A business quarter",
        EntityType.YEAR: "A specific year",
        EntityType.EVENT: "A notable event",
        EntityType.MEETING: "A scheduled meeting",
        EntityType.ENTITY: "A generic entity",
    }
    return descriptions.get(entity_type, "Unknown entity type")


def get_entity_category_mapping() -> Dict[str, callable]:
    """
    Get mapping of category names to their corresponding getter functions.
    This centralizes all entity categorization logic in one place.
    """
    return {
        "People & Roles": get_person_related_types,
        "Organizations": get_organization_related_types,
        "Projects & Products": get_project_related_types,
        "Systems & Technology": get_system_related_types,
        "Documents & Knowledge": get_document_related_types,
        "Financial": get_financial_related_types,
        "Locations": get_location_related_types,
    }


def get_entity_properties(entity_type: str) -> Dict[str, Any]:
    """
    Get additional properties for an entity type that enhance GraphRAG context.
    """
    if isinstance(entity_type, str):
        try:
            entity_enum = EntityType(entity_type)
        except ValueError:
            entity_enum = EntityType.ENTITY
    else:
        entity_enum = entity_type

    base_properties = {
        "description": get_entity_type_description(entity_enum),
        "category": _get_entity_category(entity_enum),
        "searchable": True,
        "graph_importance": _get_graph_importance(entity_enum)
    }

    type_specific_properties = {
        EntityType.PERSON: {"is_human": True, "typical_relationships": ["works_for", "manages", "reports_to"]},
        EntityType.EMPLOYEE: {"is_human": True, "typical_relationships": ["works_for", "reports_to", "member_of"]},
        EntityType.MANAGER: {"is_human": True, "leadership_role": True, "typical_relationships": ["manages", "leads"]},
        EntityType.EXECUTIVE: {"is_human": True, "leadership_role": True, "seniority_level": "executive"},
        EntityType.SKILL: {"is_abstract": True, "typical_relationships": ["possessed_by", "required_for"]},
        EntityType.COMPANY: {"is_organization": True, "typical_relationships": ["employs", "partners_with", "owns"]},
        EntityType.DEPARTMENT: {"is_organization": True, "is_internal_unit": True, "typical_relationships": ["part_of", "manages"]},
        EntityType.TEAM: {"is_organization": True, "is_internal_unit": True, "collaborative_unit": True},
        EntityType.PROJECT: {"is_initiative": True, "has_timeline": True, "typical_relationships": ["involves", "managed_by"]},
        EntityType.PRODUCT: {"is_tangible_offering": True, "typical_relationships": ["developed_by", "has_feature"]},
        EntityType.SYSTEM: {"is_technology": True, "can_be_integrated": True, "typical_relationships": ["integrates_with", "hosts"]},
        EntityType.APPLICATION: {"is_technology": True, "is_software": True, "typical_relationships": ["used_by", "runs_on"]},
        EntityType.DATABASE: {"is_technology": True, "stores_data": True, "typical_relationships": ["accessed_by", "contains"]},
        EntityType.API: {"is_technology": True, "is_interface": True, "typical_relationships": ["consumed_by", "exposes_data"]},
        EntityType.DOCUMENT: {"is_information": True, "contains_knowledge": True, "typical_relationships": ["authored_by", "references"]},
        EntityType.RISK: {"is_business_concept": True, "has_impact": True, "typical_relationships": ["identified_by", "mitigated_by"]},
        EntityType.KPI: {"is_business_concept": True, "is_measurable": True, "typical_relationships": ["tracks", "measured_by"]},
        EntityType.BUDGET: {"is_financial": True, "has_amount": True, "typical_relationships": ["allocated_to", "funded_by"]},
        EntityType.LOCATION: {"is_location": True, "geographic": True, "typical_relationships": ["contains", "located_in"]},
        EntityType.MEETING: {"is_event": True, "has_attendees": True, "typical_relationships": ["attended_by", "discusses"]},
    }
    specific_props = type_specific_properties.get(entity_enum, {})
    return {**base_properties, **specific_props}


def _get_entity_category(entity_type: EntityType) -> str:
    """Get the high-level category for an entity type."""
    # This mapping is now more comprehensive
    categories = {
        EntityType.PERSON: "People", EntityType.EMPLOYEE: "People", EntityType.MANAGER: "People",
        EntityType.EXECUTIVE: "People", EntityType.CONSULTANT: "People", EntityType.CLIENT: "People",
        EntityType.STAKEHOLDER: "People", EntityType.JOB_ROLE: "People", EntityType.SKILL: "People",
        EntityType.COMPANY: "Organizations", EntityType.DEPARTMENT: "Organizations", EntityType.TEAM: "Organizations",
        EntityType.VENDOR: "Organizations", EntityType.PARTNER: "Organizations", EntityType.COMPETITOR: "Organizations",
        EntityType.BUSINESS_UNIT: "Organizations",
        EntityType.PROJECT: "Projects & Products", EntityType.INITIATIVE: "Projects & Products",
        EntityType.PROGRAM: "Projects & Products", EntityType.CAMPAIGN: "Projects & Products",
        EntityType.PRODUCT: "Projects & Products", EntityType.SERVICE: "Projects & Products",
        EntityType.FEATURE: "Projects & Products",
        EntityType.DOCUMENT: "Documents & Knowledge", EntityType.REPORT: "Documents & Knowledge",
        EntityType.PROPOSAL: "Documents & Knowledge", EntityType.CONTRACT: "Documents & Knowledge",
        EntityType.POLICY: "Documents & Knowledge", EntityType.PROCEDURE: "Documents & Knowledge",
        EntityType.KNOWLEDGE_BASE_ARTICLE: "Documents & Knowledge", EntityType.PRESENTATION: "Documents & Knowledge",
        EntityType.SYSTEM: "Technology", EntityType.APPLICATION: "Technology", EntityType.DATABASE: "Technology",
        EntityType.PLATFORM: "Technology", EntityType.TOOL: "Technology", EntityType.TECHNOLOGY: "Technology",
        EntityType.API: "Technology", EntityType.SERVER: "Technology", EntityType.CLOUD_SERVICE: "Technology",
        EntityType.SOFTWARE_LIBRARY: "Technology", EntityType.DATA_SOURCE: "Technology",
        EntityType.PROCESS: "Business Concepts", EntityType.WORKFLOW: "Business Concepts",
        EntityType.REQUIREMENT: "Business Concepts", EntityType.OBJECTIVE: "Business Concepts",
        EntityType.GOAL: "Business Concepts", EntityType.METRIC: "Business Concepts", EntityType.KPI: "Business Concepts",
        EntityType.RISK: "Business Concepts", EntityType.OPPORTUNITY: "Business Concepts",
        EntityType.CAPABILITY: "Business Concepts",
        EntityType.BUDGET: "Financial", EntityType.COST: "Financial", EntityType.REVENUE: "Financial",
        EntityType.INVESTMENT: "Financial", EntityType.FINANCIAL_ACCOUNT: "Financial", EntityType.INVOICE: "Financial",
        EntityType.TRANSACTION: "Financial",
        EntityType.OFFICE: "Locations", EntityType.LOCATION: "Locations", EntityType.REGION: "Locations",
        EntityType.COUNTRY: "Locations", EntityType.DATA_CENTER: "Locations",
        EntityType.MILESTONE: "Time & Events", EntityType.DEADLINE: "Time & Events", EntityType.PHASE: "Time & Events",
        EntityType.QUARTER: "Time & Events", EntityType.YEAR: "Time & Events", EntityType.EVENT: "Time & Events",
        EntityType.MEETING: "Time & Events",
    }
    return categories.get(entity_type, "General")


def _get_graph_importance(entity_type: EntityType) -> float:
    """Get the graph importance score for an entity type."""
    importance_scores = {
        # High importance
        EntityType.EXECUTIVE: 1.0, EntityType.COMPANY: 0.95, EntityType.PROJECT: 0.9,
        EntityType.PRODUCT: 0.9, EntityType.SYSTEM: 0.9, EntityType.PERSON: 0.9,
        EntityType.EMPLOYEE: 0.9, EntityType.MANAGER: 0.95,
        # Medium-high importance
        EntityType.DEPARTMENT: 0.8, EntityType.TEAM: 0.8, EntityType.PROCESS: 0.8,
        EntityType.GOAL: 0.8, EntityType.CLIENT: 0.85, EntityType.CONTRACT: 0.8,
        EntityType.DATABASE: 0.8, EntityType.API: 0.8, EntityType.RISK: 0.85,
        # Medium importance
        EntityType.CONSULTANT: 0.7, EntityType.VENDOR: 0.7, EntityType.PARTNER: 0.7,
        EntityType.DOCUMENT: 0.7, EntityType.REPORT: 0.7, EntityType.APPLICATION: 0.75,
        EntityType.SKILL: 0.7, EntityType.KPI: 0.75, EntityType.BUDGET: 0.7,
        # Lower importance
        EntityType.TOOL: 0.6, EntityType.PROCEDURE: 0.6, EntityType.METRIC: 0.6,
        EntityType.OFFICE: 0.6, EntityType.LOCATION: 0.6, EntityType.MEETING: 0.6,
        # Contextual importance
        EntityType.MILESTONE: 0.5, EntityType.DEADLINE: 0.5, EntityType.PHASE: 0.5,
        EntityType.QUARTER: 0.4, EntityType.YEAR: 0.3, EntityType.INVOICE: 0.5,
    }
    return importance_scores.get(entity_type, 0.5)

