"""
Generic Context Search Interface

This module provides a standardized interface for context search functionality
that can be used by any connector or service, ensuring consistent search behavior
across the entire system.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class SearchScope(Enum):
    """Enumeration of search scopes."""
    USER = "user"
    ORGANIZATION = "organization"
    AGENT = "agent"
    GLOBAL = "global"


class SearchType(Enum):
    """Enumeration of search types."""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    GRAPH = "graph"


@dataclass
class SearchRequest:
    """
    Standardized search request structure.
    """
    user_id: str
    query_text: str
    organisation_id: str
    top_k: int = 5
    agent_id: Optional[str] = None
    file_ids: Optional[List[str]] = None
    connector_types: Optional[List[str]] = None
    search_type: SearchType = SearchType.HYBRID
    search_scope: SearchScope = SearchScope.USER
    include_metadata: bool = True
    include_entities: bool = False
    include_relationships: bool = False
    min_score: float = 0.0
    max_age_days: Optional[int] = None


@dataclass
class SearchResult:
    """
    Standardized search result structure.
    """
    file_id: str
    file_name: str
    score: float
    vector_id: str
    chunk_text: str
    connector_type: str
    mime_type: Optional[str] = None
    web_view_link: Optional[str] = None
    created_time: Optional[str] = None
    modified_time: Optional[str] = None
    search_type: Optional[str] = None
    entities: Optional[List[Dict[str, Any]]] = None
    relationships: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class SearchResponse:
    """
    Standardized search response structure.
    """
    success: bool
    message: str
    results: List[SearchResult]
    total_results: int
    search_time_ms: float
    query_analysis: Optional[Dict[str, Any]] = None


@dataclass
class BatchSearchRequest:
    """
    Standardized batch search request structure.
    """
    user_id: str
    query_texts: List[str]
    organisation_id: str
    top_k: int = 5
    agent_id: Optional[str] = None
    file_ids: Optional[List[str]] = None
    connector_types: Optional[List[str]] = None
    search_type: SearchType = SearchType.HYBRID
    search_scope: SearchScope = SearchScope.USER
    include_metadata: bool = True
    include_entities: bool = False
    include_relationships: bool = False
    min_score: float = 0.0
    max_age_days: Optional[int] = None


@dataclass
class BatchSearchResponse:
    """
    Standardized batch search response structure.
    """
    success: bool
    message: str
    results: List[List[SearchResult]]  # List of results for each query
    total_queries: int
    total_results: int
    search_time_ms: float


class IContextSearchProvider(ABC):
    """
    Interface for context search providers.
    
    This interface defines the contract that all search providers must implement
    to ensure consistent search functionality across different implementations.
    """
    
    @abstractmethod
    def search(self, request: SearchRequest) -> SearchResponse:
        """
        Perform a context search based on the provided request.
        
        Args:
            request: The search request containing all search parameters
            
        Returns:
            SearchResponse containing the search results
        """
        pass
    
    @abstractmethod
    def batch_search(self, request: BatchSearchRequest) -> BatchSearchResponse:
        """
        Perform batch context search for multiple queries.
        
        Args:
            request: The batch search request containing multiple queries
            
        Returns:
            BatchSearchResponse containing results for all queries
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        Check if the search provider is available and ready to serve requests.
        
        Returns:
            Boolean indicating if the provider is available
        """
        pass
    
    @abstractmethod
    def get_supported_search_types(self) -> List[SearchType]:
        """
        Get the list of search types supported by this provider.
        
        Returns:
            List of supported search types
        """
        pass
    
    @abstractmethod
    def get_supported_connector_types(self) -> List[str]:
        """
        Get the list of connector types supported by this provider.
        
        Returns:
            List of supported connector types
        """
        pass


class IConnectorSearchCapable(ABC):
    """
    Interface for connectors that support search functionality.
    
    This interface should be implemented by connectors that want to provide
    their own search capabilities in addition to the global search service.
    """
    
    @abstractmethod
    def search_documents(self, request: SearchRequest) -> SearchResponse:
        """
        Search documents within this specific connector.
        
        Args:
            request: The search request
            
        Returns:
            SearchResponse containing connector-specific results
        """
        pass
    
    @abstractmethod
    def get_searchable_file_count(self, user_id: str, organisation_id: str) -> int:
        """
        Get the number of searchable files in this connector.
        
        Args:
            user_id: The user ID
            organisation_id: The organization ID
            
        Returns:
            Number of searchable files
        """
        pass
    
    @abstractmethod
    def supports_search_type(self, search_type: SearchType) -> bool:
        """
        Check if this connector supports a specific search type.
        
        Args:
            search_type: The search type to check
            
        Returns:
            Boolean indicating if the search type is supported
        """
        pass


class SearchProviderRegistry:
    """
    Registry for managing multiple search providers.
    
    This allows the system to use different search providers for different
    types of queries or data sources.
    """
    
    def __init__(self):
        self._providers: Dict[str, IContextSearchProvider] = {}
        self._default_provider: Optional[str] = None
    
    def register_provider(self, name: str, provider: IContextSearchProvider, is_default: bool = False):
        """
        Register a search provider.
        
        Args:
            name: Name of the provider
            provider: The provider instance
            is_default: Whether this should be the default provider
        """
        self._providers[name] = provider
        if is_default or self._default_provider is None:
            self._default_provider = name
    
    def get_provider(self, name: Optional[str] = None) -> Optional[IContextSearchProvider]:
        """
        Get a search provider by name.
        
        Args:
            name: Name of the provider (uses default if None)
            
        Returns:
            The provider instance or None if not found
        """
        if name is None:
            name = self._default_provider
        return self._providers.get(name)
    
    def get_available_providers(self) -> List[str]:
        """Get list of available provider names."""
        return [name for name, provider in self._providers.items() if provider.is_available()]
    
    def get_default_provider_name(self) -> Optional[str]:
        """Get the name of the default provider."""
        return self._default_provider


# Global search provider registry
search_provider_registry = SearchProviderRegistry()


def get_search_provider_registry() -> SearchProviderRegistry:
    """Get the global search provider registry."""
    return search_provider_registry
